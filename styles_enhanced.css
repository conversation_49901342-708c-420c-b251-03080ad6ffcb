/* Enhanced WFG Data Entry System - Additional Styles */

/* Custom Properties for consistent theming */
:root {
  --primary-gradient: linear-gradient(135deg, #0466C8 0%, #003B73 100%);
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Enhanced Focus Styles */
*:focus {
  outline: none;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(4, 102, 200, 0.1);
  border-color: var(--primary-color);
}

/* Enhanced Button Styles */
.btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-danger {
  background: var(--error-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Enhanced Input Styles */
.form-group input,
.form-group textarea,
.form-group select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Enhanced Error States */
.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Enhanced Loading States */
.loading {
  pointer-events: none;
  opacity: 0.6;
  cursor: wait;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Enhanced Table Styles */
.data-table {
  position: relative;
}

.data-table tbody tr {
  transition: all 0.2s ease;
}

.data-table tbody tr:hover {
  background: #f8fafc;
  transform: scale(1.005);
  box-shadow: var(--shadow-sm);
}

.data-table th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #f8fafc;
  backdrop-filter: blur(10px);
}

/* Enhanced Modal Styles */
.modal {
  backdrop-filter: blur(4px);
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
  box-shadow: var(--shadow-xl);
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Card Styles */
.card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Enhanced Sidebar Animations */
.sidebar-menu a {
  position: relative;
  overflow: hidden;
}

.sidebar-menu a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.sidebar-menu a:hover::before {
  left: 100%;
}

/* Enhanced Toast Styles */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: none;
}

.toast {
  pointer-events: auto;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast.success {
  background: rgba(16, 185, 129, 0.9);
}

.toast.error {
  background: rgba(239, 68, 68, 0.9);
}

.toast.warning {
  background: rgba(245, 158, 11, 0.9);
}

.toast.info {
  background: rgba(4, 102, 200, 0.9);
}

/* Enhanced Progress Indicators */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Form Sections */
.form-section {
  position: relative;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: #f8fafc;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.form-section:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  box-shadow: var(--shadow-sm);
}

.form-section .section-title {
  margin-top: 0;
  color: #1e293b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-section .section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--primary-gradient);
  border-radius: 2px;
}

/* Enhanced Checkbox Styles */
.checkbox-wrapper {
  position: relative;
  margin: 0.5rem 0;
}

.checkbox-wrapper input[type="checkbox"] {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  margin-right: 0.75rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

.checkbox-wrapper input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

.checkbox-wrapper input[type="checkbox"]:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(4, 102, 200, 0.1);
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-indicator.error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-indicator.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-indicator.info {
  background: rgba(4, 102, 200, 0.1);
  color: #0353A4;
  border: 1px solid rgba(4, 102, 200, 0.2);
}

/* Enhanced Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge.primary {
  background: var(--primary-gradient);
  color: white;
}

.badge.success {
  background: var(--success-gradient);
  color: white;
}

.badge.error {
  background: var(--error-gradient);
  color: white;
}

.badge.warning {
  background: var(--warning-gradient);
  color: white;
}

/* Enhanced Responsive Utilities */
@media (max-width: 640px) {
  .hide-mobile {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
  
  .mobile-text-center {
    text-align: center !important;
  }
}

@media (min-width: 641px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Enhanced Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .auto-dark .card {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .auto-dark .form-container {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .auto-dark input,
  .auto-dark textarea,
  .auto-dark select {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .header,
  .btn,
  .no-print {
    display: none !important;
  }
  
  .main-content {
    margin-left: 0 !important;
  }
  
  .page-content {
    padding: 0 !important;
  }
  
  .form-container {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .btn-primary {
    background: #000 !important;
    color: #fff !important;
    border: 2px solid #fff !important;
  }
  
  .form-group input,
  .form-group textarea,
  .form-group select {
    border: 2px solid #000 !important;
  }
  
  .sidebar {
    background: #000 !important;
  }
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Enhanced Utility Classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}