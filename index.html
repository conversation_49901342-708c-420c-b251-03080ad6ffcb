<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WFG Data Entry System - Enhanced</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="styles_enhanced.css" rel="stylesheet">
  
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'dark-blue': '#002855',
            'navy': '#003B73',
            'medium-blue': '#0353A4',
            'blue': '#0466C8',
            'light-blue': '#0582CA',
            'success': '#10b981',
            'error': '#ef4444',
            'warning': '#f59e0b',
          },
          fontFamily: {
            'sans': ['Inter', 'sans-serif'],
          },
          animation: {
            'fadeIn': 'fadeIn 0.3s ease-in-out',
            'slideIn': 'slideIn 0.3s ease-out',
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            },
            slideIn: {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(0)' },
            }
          }
        }
      }
    }
  </script>
  <style>
    body {
      background-color: #f9fafb;
      color: #1f2937;
      font-family: 'Inter', sans-serif;
      transition: all 0.3s ease;
      overflow-x: hidden; /* Prevent horizontal scrolling */
      margin: 0;
      padding: 0;
    }
    .sidebar {
      background-color: #002855;
      height: 100vh;
      width: 260px;
      position: fixed;
      top: 0;
      left: 0;
      padding: 1.5rem;
      color: white;
      display: flex;
      flex-direction: column;
      transform: translateX(0);
      transition: transform 0.3s ease-in-out;
      z-index: 1000;
      box-shadow: 2px 0 5px rgba(0,0,0,0.2);
    }
    .sidebar.hidden {
      transform: translateX(-100%);
    }
    .sidebar-header {
      display: flex;
      align-items: center;
      margin-bottom: 2rem;
    }
    .sidebar-header img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 0.75rem;
      object-fit: cover;
    }
    .sidebar-header h2 {
      font-size: 1.25rem;
      font-weight: 700;
    }
    .sidebar-menu a {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      margin-bottom: 0.5rem;
      border-radius: 0.5rem;
      color: white;
      text-decoration: none;
      transition: background-color 0.2s ease;
    }
    .sidebar-menu a i {
      margin-right: 0.75rem;
      font-size: 1.1rem;
    }
    .sidebar-menu a:hover, .sidebar-menu a.active {
      background-color: #003B73; /* navy */
    }
    .sidebar-menu a.active {
      font-weight: 600;
    }
    .main-content {
      margin-left: 260px;
      transition: margin-left 0.3s ease-in-out;
      padding: 1rem 2rem;
      flex-grow: 1;
    }
    .main-content.full-width {
      margin-left: 0;
    }
    .header {
      background-color: #ffffff;
      padding: 1rem 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      top: 0;
      z-index: 999;
    }
    .menu-toggle {
      background: none;
      border: none;
      color: #1f2937;
      font-size: 1.5rem;
      cursor: pointer;
      display: none; /* Hidden on desktop */
    }
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #333;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      opacity: 0;
      transition: opacity 0.5s ease-in-out;
      z-index: 1000;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .toast.show {
      opacity: 1;
    }
    .toast.success {
      background-color: #10b981; /* success */
    }
    .toast.error {
      background-color: #ef4444; /* error */
    }
    .loading-overlay {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
    }
    .spinner {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
    }
    .form-container {
      background-color: white;
      padding: 2rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 900px; /* Adjust max-width for forms */
      margin: 2rem auto;
    }
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }
    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
    }
    .form-group input, .form-group textarea, .form-group select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      font-size: 1rem;
      color: #1f2937;
      background-color: #f9fafb;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }
    .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
      border-color: #0466C8;
      box-shadow: 0 0 0 3px rgba(4, 102, 200, 0.2);
      outline: none;
    }
    textarea {
      resize: vertical;
      min-height: 80px;
    }
    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
      font-size: 1rem;
    }
    .btn-primary {
      background-color: #0466C8;
      color: white;
      border: 1px solid #0466C8;
    }
    .btn-primary:hover {
      background-color: #0353A4;
    }
    .btn-secondary {
      background-color: #e5e7eb;
      color: #374151;
      border: 1px solid #d1d5db;
    }
    .btn-secondary:hover {
      background-color: #d1d5db;
    }
    .fixed-header {
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: #f9fafb; /* Match body background or slightly different */
      padding-bottom: 1rem; /* Space between header and content */
      margin-bottom: 1rem;
      width: calc(100vw - 260px); /* Adjust for sidebar width */
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      min-height: 100vh; /* Full viewport height */
    }
    
    .content {
      flex-grow: 1; /* Allow content to take remaining space */
      padding: 2rem;
      width: calc(100vw - 260px);
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      min-height: 100vh; /* Full viewport height */
    }
    
    .content.full {
      margin-left: 0;
      max-width: 100vw;
    }
    
    /* Mobile adjustments */
    @media (max-width: 768px) {
      .fixed-header {
        width: calc(100% - 2rem); /* Account for smaller padding on mobile */
      }
      
      .content {
        padding: 1rem;
      }

      .sidebar {
        width: 200px;
      }

      .main-content {
        margin-left: 0; /* Always start at 0 on mobile */
        padding: 1rem;
      }
      .menu-toggle {
        display: block; /* Show menu toggle on mobile */
      }
      .sidebar.hidden + .main-content {
        margin-left: 0;
      }
    }

    /* Styles for login form */
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f0f2f5;
    }
    .login-box {
        background-color: #ffffff;
        padding: 3rem;
        border-radius: 0.75rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 400px;
        text-align: center;
    }
    .login-box h2 {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1.5rem;
    }
    .login-box .form-group {
        margin-bottom: 1.25rem;
        text-align: left;
    }
    .login-box .form-group label {
        font-size: 0.875rem;
        color: #4b5563;
        margin-bottom: 0.5rem;
    }
    .login-box .form-group input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 1rem;
        background-color: #f9fafb;
    }
    .login-box .btn-login {
        width: 100%;
        padding: 0.85rem;
        background-color: #0466C8;
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    .login-box .btn-login:hover {
        background-color: #0353A4;
    }
    .login-error {
        color: #ef4444; /* Tailwind error red */
        margin-top: 1rem;
        font-size: 0.875rem;
        display: none; /* Hidden by default */
    }
    .page-title {
      font-size: 1.875rem; /* text-3xl */
      font-weight: 700; /* font-bold */
      color: #1f2937; /* text-gray-900 */
      margin-bottom: 1.5rem; /* mb-6 */
    }
    .page-description {
        color: #4b5563; /* text-gray-600 */
        margin-bottom: 2rem; /* mb-8 */
    }
    .card {
        background-color: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        margin-bottom: 1.5rem;
    }
    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1rem;
    }
    .card-content {
        color: #4b5563;
    }
    .stat-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    .stat-card {
        background-color: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        text-align: center;
    }
    .stat-card .icon {
        font-size: 2.5rem;
        color: #0466C8;
        margin-bottom: 0.75rem;
    }
    .stat-card .value {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }
    .stat-card .label {
        font-size: 0.875rem;
        color: #4b5563;
    }

    /* Table styles for multi-entry forms */
    .data-table-container {
        overflow-x: auto;
        margin-top: 1.5rem;
    }
    .data-table {
        width: 100%;
        border-collapse: collapse;
        min-width: 700px; /* Ensure table is not too squished on smaller screens */
    }
    .data-table th, .data-table td {
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        text-align: left;
        white-space: nowrap; /* Prevent text wrapping in cells */
    }
    .data-table th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
        font-size: 0.875rem;
        text-transform: uppercase;
    }
    .data-table td {
        background-color: white;
        color: #374151;
        font-size: 0.9rem;
    }
    .data-table tr:nth-child(even) td {
        background-color: #f9fafb;
    }
    .data-table tr:hover td {
        background-color: #f3f4f6;
    }
    .table-actions button {
        background: none;
        border: none;
        color: #0466C8;
        cursor: pointer;
        font-size: 1.1rem;
        margin-right: 0.5rem;
        transition: color 0.2s ease;
    }
    .table-actions button:hover {
        color: #0353A4;
    }
    
    /* Dreams List form controls */
    .data-table .form-control {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        color: #374151;
        background-color: #ffffff;
    }
    
    .data-table .form-control:focus {
        border-color: #0466C8;
        box-shadow: 0 0 0 2px rgba(4, 102, 200, 0.2);
        outline: none;
    }
    
    .add-entry-btn {
        background-color: #10b981;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 600;
        cursor: pointer;
        margin-bottom: 1.5rem;
        transition: background-color 0.2s ease;
    }
    
    .add-entry-btn:hover {
        background-color: #059669;
    }
    
    /* User Management specific styling - Now properly positioned */
    
    #user-management-form .form-container {
      background-color: white;
      padding: 2rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 900px; /* Same as other forms */
      margin: 2rem auto; /* Same as other forms */
    }
    
    #user-management-form .form-group label {
      color: #374151;
      font-weight: 500;
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
    }
    
    #user-management-form .form-group input,
    #user-management-form .form-group select {
      border: 2px solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      transition: all 0.3s ease;
      width: 100%;
    }
    
    #user-management-form .form-group input:focus,
    #user-management-form .form-group select:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    /* Card hover effects */
    #user-management-form .bg-gradient-to-br {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    #user-management-form .bg-gradient-to-br:hover {
      transform: translateY(-2px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    /* Button animations */
    #user-management-form button {
      transition: all 0.3s ease;
    }
    
    #user-management-form button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    /* Table responsive design */
    @media (max-width: 768px) {
      #user-management-form .form-container {
        margin: 1rem !important;
        padding: 1rem !important;
      }
      
      #user-management-form .grid {
        grid-template-columns: 1fr;
      }
      
      #user-management-form .overflow-x-auto {
        margin: -1rem;
        padding: 1rem;
      }
    }
    .table-actions button.delete-btn {
        color: #ef4444;
    }
    .table-actions button.delete-btn:hover {
        color: #b91c1c;
    }
    .add-entry-btn {
      margin-top: 1.5rem;
      background-color: #22c55e; /* green-500 */
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 600;
      cursor: pointer;
      border: none;
      transition: background-color 0.2s ease;
    }
    .add-entry-btn:hover {
      background-color: #16a34a; /* green-600 */
    }

    /* Modal styles */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease;
    }
    .modal.show {
      opacity: 1;
      visibility: visible;
    }
    .modal-content {
      background-color: white;
      padding: 2rem;
      border-radius: 0.5rem;
      width: 90%;
      max-width: 500px;
      position: relative;
    }
    .modal-close-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
      font-size: 1.5rem;
      background: none;
      border: none;
      cursor: pointer;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 2rem;
    }

    /* Checkbox styling */
    .checkbox-wrapper {
      display: flex;
      align-items: center;
      margin-top: 0.25rem;
    }
    
    .checkbox-wrapper input[type="checkbox"] {
      width: 1.25rem;
      height: 1.25rem;
      margin-right: 0.5rem;
      cursor: pointer;
      accent-color: #0466C8;
    }
    
    .checkbox-label {
      font-size: 0.9rem;
      color: #4b5563;
    }
    
    /* Page navigation CSS */
    .page-content {
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
      display: block;
    }
    
    .page-content.hidden {
      display: none;
    }
    
    /* Ensure forms are visible when not hidden */
    .page-content:not(.hidden) {
      display: block !important;
      opacity: 1 !important;
    }
    
    /* Override Tailwind's hidden class for our page-content */
    .page-content.hidden {
      display: none !important;
    }
    
    /* Make sure forms are properly positioned */
    .form-container {
      position: relative;
      z-index: 1;
    }
    
    /* Form styling */
    .form-container {
      max-width: 100%;
      margin: 0 auto;
      padding: 1rem;
    }
    
    .page-title {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: #1f2937;
    }
    
    .page-description {
      color: #6b7280;
      margin-bottom: 2rem;
    }
  </style>

</head>
<body class="bg-gray-50 flex">
  <div id="toast" class="toast"></div>

  <div id="loginPage" class="login-container">
    <div class="login-box">
      <img src="https://lh3.googleusercontent.com/pw/AP1G8A02j9K0L8X0D8N1X_K_0r5t4F7o2tQ9L4u7x7N6S2Z0N8M5F1P4U1L5N6Q1-G9Y3O2V6A5U1-B7C8E2J0P3A4D6S7L8K9O0Q2W4X5Y6Z7A=_s300-pa-no?authuser=0" alt="Logo" class="mx-auto w-24 h-24 mb-4">
      <h2 class="text-3xl font-bold mb-2 text-gradient">Welcome to WFG</h2>
      <p class="text-gray-600 mb-6">Enhanced Data Entry System</p>
      <form id="loginForm">
        <div class="form-group">
          <label for="agentName" class="block text-gray-700 text-sm font-bold mb-2">Agent Name:</label>
          <input type="text" id="agentName" name="agentName" class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
        </div>
        <div class="form-group">
          <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password:</label>
          <input type="password" id="password" name="password" class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline" required>
        </div>
        <p id="loginError" class="login-error">Invalid agent name or password.</p>
        <button type="submit" class="btn-login">
          <span class="mr-2">Sign In</span>
          <i class="fas fa-sign-in-alt"></i>
        </button>
      </form>
      
      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div class="text-sm text-green-800">
          <p><strong>✅ Enhanced Features:</strong></p>
          <ul class="list-disc list-inside mt-1 space-y-1">
            <li>Fixed user name display issue</li>
            <li>Forms now load existing data properly</li>
            <li>Enhanced error handling and feedback</li>
            <li>Improved mobile responsiveness</li>
            <li>Auto-save functionality</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div id="dashboardPage" class="hidden w-full flex">
    <aside id="sidebar" class="sidebar">
      <div class="sidebar-header">
        <img id="agentAvatar" src="https://ui-avatars.com/api/?name=User&background=random" alt="Agent Avatar" class="rounded-full w-10 h-10 object-cover">
        <div>
          <h2 id="agentNameDisplay">Agent Name</h2>
          <span id="agentRoleDisplay" class="text-xs text-gray-300">Role</span>
        </div>
      </div>
      <nav class="sidebar-menu flex-grow">
        <a href="#" data-page="dashboard" class="active"><i class="fas fa-home"></i> Dashboard</a>
        <a href="#" data-page="personal-details" class=""><i class="fas fa-user-circle"></i> Personal Details</a>
        <a href="#" data-page="system-progressions" class=""><i class="fas fa-chart-line"></i> System Progressions</a>
        <a href="#" data-page="licensing-checklist" class=""><i class="fas fa-clipboard-check"></i> Licensing Checklist</a>
        <a href="#" data-page="dreams-list" class=""><i class="fas fa-lightbulb"></i> Dreams List</a>
        <a href="#" data-page="expenses-to-income-report" class=""><i class="fas fa-money-bill-wave"></i> Expenses Report</a>
        <a href="#" data-page="potential-business-partners" class=""><i class="fas fa-handshake"></i> Business Partners</a>
        <a href="#" data-page="potential-field-trainings" class=""><i class="fas fa-chalkboard-teacher"></i> Field Trainings</a>
        <a href="#" data-page="career-progression" class=""><i class="fas fa-chart-line"></i> Career Progression</a>
        <!-- Add User Management link for admin only -->
        <a href="#" data-page="user-management" class="admin-only"><i class="fas fa-users-cog"></i> User Management</a>
      </nav>
      <div class="mt-auto">
        <a href="#" id="logoutButton" class="mt-4 flex items-center p-3 rounded-md text-white hover:bg-navy transition">
          <i class="fas fa-sign-out-alt mr-3"></i> Logout
        </a>
      </div>
    </aside>

    <div id="mainContent" class="main-content flex flex-col">
      <header class="header flex justify-between items-center bg-white p-4 shadow-sm">
        <button id="menuToggle" class="menu-toggle">
          <i class="fas fa-bars"></i>
        </button>
        <h1 class="text-xl font-semibold text-gray-800">Welcome, <span id="headerAgentName">Agent</span>!</h1>
        <div class="text-right">
          <p id="currentDate" class="text-sm text-gray-600"></p>
          <p id="currentTime" class="text-sm text-gray-600"></p>
        </div>
      </header>

      <div class="flex-grow p-6">
        <div id="dashboardPageContent" class="page-content hidden">
          <h1 class="page-title">Welcome to WFG Data Entry System</h1>
          <p class="page-description">Please use the navigation menu to access the forms and manage your data.</p>
        </div>

        <div id="personal-details-form" class="page-content hidden">
          <div class="form-container">
            <h2 class="page-title">Personal Details</h2>
            <p class="page-description">Enter your personal information and licensing details.</p>
            
            <form id="personalForm" class="form-grid">
              <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" name="name" required>
              </div>
              <div class="form-group">
                <label for="date">Date:</label>
                <input type="date" id="date" name="date" required>
              </div>
              <div class="form-group">
                <label for="agentId">Agent ID:</label>
                <input type="text" id="agentId" name="agent_id" required>
              </div>
              <div class="form-group">
                <label for="state">State:</label>
                <input type="text" id="state" name="state" required>
              </div>
              <div class="form-group">
                <label for="npn">NPN:</label>
                <input type="text" id="npn" name="npn">
              </div>
              <div class="form-group">
                <label for="number">Number:</label>
                <input type="text" id="number" name="number">
              </div>
              <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email">
              </div>
              <div class="form-group">
                <label for="children">Number of Children:</label>
                <input type="number" id="children" name="children" min="0">
              </div>
              <div class="form-group">
                <label for="examDate">Exam Date:</label>
                <input type="date" id="examDate" name="exam_date">
              </div>
              <!-- Hidden record_id field -->
              <input type="hidden" id="personalRecordId" name="record_id">
              <div class="form-actions col-span-full">
                <p id="personalFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                <button type="button" class="btn btn-secondary auto-save-toggle mr-2" onclick="toggleAutoSave()">Disable Auto-Save</button>
                <button type="submit" class="btn btn-primary">Save Personal Details</button>
              </div>
            </form>
          </div>
        </div>

        <div id="system-progressions-form" class="page-content hidden">
          <div class="form-container">
            <h2 class="page-title">System Progressions</h2>
            <p class="page-description">Track your progress through the WFG system.</p>
            <form id="progressionsForm" class="form-grid">
                <div class="form-group">
                    <label for="codeNumber">Code Number:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="codeNumber" name="code_number" value="Yes">
                        <span class="checkbox-label">Completed</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="client">Client Name:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="client" name="client" value="Yes">
                        <span class="checkbox-label">Completed</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="passLicense">Pass License:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="passLicense" name="pass_license" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="businessPartnerPlan">Business Partner Plan:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="businessPartnerPlan" name="business_partner_plan" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="licensedAppointed">Licensed Appointed:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="licensedAppointed" name="licensed_appointed" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="fieldTrainings10">Field Trainings (10):</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="fieldTrainings10" name="field_trainings_10" value="Yes">
                        <span class="checkbox-label">Completed</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="associatePromotion">Associate Promotion:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="associatePromotion" name="associate_promotion" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="netLicense">Net License:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="netLicense" name="net_license" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="completeLaserFund">Complete Laser Fund:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="completeLaserFund" name="complete_laser_fund" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="cftInProgress">CFT In Progress:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="cftInProgress" name="cft_in_progress" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="certifiedFieldTrainer">Certified Field Trainer:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="certifiedFieldTrainer" name="certified_field_trainer" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="eliteTrainer">Elite Trainer:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="eliteTrainer" name="elite_trainer" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="marketingDirector">Marketing Director:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="marketingDirector" name="marketing_director" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="watch50000">Watch 50,000:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="watch50000" name="watch_50000" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="ring100000">Ring 100,000:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="ring100000" name="ring_100000" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="executiveMarketingDirector">Executive Marketing Director:</label>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" id="executiveMarketingDirector" name="executive_marketing_director" value="Yes">
                        <span class="checkbox-label">Yes</span>
                    </div>
                </div>
                <input type="hidden" id="progressionsRecordId" name="record_id">
                <input type="hidden" id="progressionsAgentName" name="agentName">
                <div class="form-actions col-span-full">
                  <p id="progressionsFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                  <button type="submit" class="btn btn-primary">Save Progressions</button>
                </div>
            </form>
          </div>
        </div>

        <div id="licensing-checklist-form" class="page-content hidden">
          <div class="form-container">
            <h2 class="page-title">Licensing Checklist</h2>
            <p class="page-description">Track your licensing requirements and progress.</p>
            <form id="licensingForm" class="form-grid">
              <div class="form-group">
                <label for="preLicensingCourse">Pre Licensing Course:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="preLicensingCourse" name="pre_licensing_course" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="scheduleLifeQuiz">Schedule Life Quiz:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="scheduleLifeQuiz" name="schedule_life_quiz" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="fingerprints">Fingerprints (If Applicable):</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="fingerprints" name="fingerprints" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="applyToState">Apply to State (NIPR):</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="applyToState" name="apply_to_state" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="submitToGFI">Submit to GFI:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="submitToGFI" name="submit_to_gfi" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="completeCECourses">Complete CE Courses:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="completeCECourses" name="complete_ce_courses" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="errorsAndOmissions">Errors & Omissions:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="errorsAndOmissions" name="errors_and_omissions" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="becomeFullyAppointed">Become fully appointed with all companies:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="becomeFullyAppointed" name="become_fully_appointed" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <div class="form-group">
                <label for="setupDirectDeposit">Set up Direct Deposit:</label>
                <div class="checkbox-wrapper">
                  <input type="checkbox" id="setupDirectDeposit" name="setup_direct_deposit" value="true">
                  <span class="checkbox-label">Completed</span>
                </div>
              </div>
              <input type="hidden" id="licensingRecordId" name="record_id">
              <input type="hidden" id="licensingAgentName" name="agentName">
              <div class="form-actions col-span-full">
                <p id="licensingFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                <button type="submit" class="btn btn-primary">Save Licensing Checklist</button>
              </div>
            </form>
          </div>
        </div>

        <div id="career-progression-form" class="page-content hidden">
          <div class="form-container">
            <h2 class="page-title">Career Progression Checklist</h2>
            <p class="page-description">Track your career progression steps and achievements.</p>
            <form id="careerProgressionForm" class="form-grid">
              <!-- Phase 1: Onboarding -->
              <div class="form-section">
                <h3 class="section-title">Phase 1: Initial Onboarding</h3>
                <div class="form-group">
                  <label for="step1Onboarding">Step 1 Onboarding:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="step1Onboarding" name="step1_onboarding" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="licensingClass">Licensing Class:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="licensingClass" name="licensing_class" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="personalFinancialReview">Personal Financial Review:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="personalFinancialReview" name="personal_financial_review" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="moneyWealthLifeInsurance">Money, Wealth & Life Insurance:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="moneyWealthLifeInsurance" name="money_wealth_life_insurance" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
              </div>

              <!-- Phase 2: Business Partners -->
              <div class="form-section">
                <h3 class="section-title">Phase 2: Business Partners</h3>
                <div class="form-group">
                  <label for="step2Onboarding">Step 2 Onboarding:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="step2Onboarding" name="step2_onboarding" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="businessPartnerList">Business Partner List:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="businessPartnerList" name="business_partner_list" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="businessPartner1">Business Partner 1:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="businessPartner1" name="business_partner_1" value="true">
                    <span class="checkbox-label">Recruited</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="businessPartner2">Business Partner 2:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="businessPartner2" name="business_partner_2" value="true">
                    <span class="checkbox-label">Recruited</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="businessPartner3">Business Partner 3:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="businessPartner3" name="business_partner_3" value="true">
                    <span class="checkbox-label">Recruited</span>
                  </div>
                </div>
              </div>

              <!-- Phase 3: Licensing -->
              <div class="form-section">
                <h3 class="section-title">Phase 3: Licensing</h3>
                <div class="form-group">
                  <label for="passLifeLicenseTest">Pass Life License Test:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="passLifeLicenseTest" name="pass_life_license_test" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fingerprintsApplyLicense">Fingerprints & Apply for License:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fingerprintsApplyLicense" name="fingerprints_apply_license" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="introToEmd">Intro to EMD:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="introToEmd" name="intro_to_emd" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
              </div>

              <!-- Phase 4: Field Training -->
              <div class="form-section">
                <h3 class="section-title">Phase 4: Field Training</h3>
                <div class="form-group">
                  <label for="step3Onboarding">Step 3 Onboarding:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="step3Onboarding" name="step3_onboarding" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTrainingList">Field Training List:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTrainingList" name="field_training_list" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="masterEthorScript">Master ETHOR Script:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="masterEthorScript" name="master_ethor_script" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="set10FieldTrainings">Set 10 Field Trainings:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="set10FieldTrainings" name="set_10_field_trainings" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
              </div>

              <!-- Field Trainings -->
              <div class="form-section">
                <h3 class="section-title">Field Training Appointments</h3>
                <div class="form-group">
                  <label for="fieldTraining1">Field Training 1:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining1" name="field_training_1" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining2">Field Training 2:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining2" name="field_training_2" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining3">Field Training 3:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining3" name="field_training_3" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining4">Field Training 4:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining4" name="field_training_4" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining5">Field Training 5:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining5" name="field_training_5" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining6">Field Training 6:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining6" name="field_training_6" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining7">Field Training 7:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining7" name="field_training_7" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining8">Field Training 8:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining8" name="field_training_8" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining9">Field Training 9:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining9" name="field_training_9" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="fieldTraining10">Field Training 10:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="fieldTraining10" name="field_training_10" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
              </div>

              <!-- Promotions and Achievements -->
              <div class="form-section">
                <h3 class="section-title">Promotions & Achievements</h3>
                <div class="form-group">
                  <label for="unlockAccessToCourses">Unlock Access to Courses:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="unlockAccessToCourses" name="unlock_access_to_courses" value="true">
                    <span class="checkbox-label">Completed</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="associatePromotion">Associate Promotion:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="associatePromotion" name="associate_promotion" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="seniorAssociatePromotion">Senior Associate Promotion:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="seniorAssociatePromotion" name="senior_associate_promotion" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="marketingDirector">Marketing Director:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="marketingDirector" name="marketing_director" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
              </div>

              <!-- Licenses and Certifications -->
              <div class="form-section">
                <h3 class="section-title">Licenses & Certifications</h3>
                <div class="form-group">
                  <label for="netLicense">Net License:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="netLicense" name="net_license" value="true">
                    <span class="checkbox-label">Obtained</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="license1">License 1:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="license1" name="license_1" value="true">
                    <span class="checkbox-label">Obtained</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="license2">License 2:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="license2" name="license_2" value="true">
                    <span class="checkbox-label">Obtained</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="license3">License 3:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="license3" name="license_3" value="true">
                    <span class="checkbox-label">Obtained</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="license4">License 4:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="license4" name="license_4" value="true">
                    <span class="checkbox-label">Obtained</span>
                  </div>
                </div>
              </div>

              <!-- Recognition Levels -->
              <div class="form-section">
                <h3 class="section-title">Recognition Levels</h3>
                <div class="form-group">
                  <label for="watch10000">Watch $10,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="watch10000" name="watch_10000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="watch20000">Watch $20,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="watch20000" name="watch_20000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="watch30000">Watch $30,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="watch30000" name="watch_30000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="watch40000">Watch $40,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="watch40000" name="watch_40000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="watch50000">Watch $50,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="watch50000" name="watch_50000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="ring60000">Ring $60,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="ring60000" name="ring_60000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="ring70000">Ring $70,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="ring70000" name="ring_70000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="ring80000">Ring $80,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="ring80000" name="ring_80000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="ring90000">Ring $90,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="ring90000" name="ring_90000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="ring100000">Ring $100,000:</label>
                  <div class="checkbox-wrapper">
                    <input type="checkbox" id="ring100000" name="ring_100000" value="true">
                    <span class="checkbox-label">Achieved</span>
                  </div>
                </div>
              </div>

              <input type="hidden" id="careerProgressionRecordId" name="record_id">
              <input type="hidden" id="careerProgressionAgentName" name="agentName">
              <div class="form-actions col-span-full">
                <p id="careerProgressionFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                <button type="submit" class="btn btn-primary">Save Progress</button>
              </div>
            </form>
          </div>
        </div>

        <div id="dreams-list-form" class="page-content hidden">
            <div class="form-container">
                <h2 class="page-title">Dreams List</h2>
                <p class="page-description">Add and manage your personal dreams and goals.</p>
                
                <form id="dreamsForm">
                    <button type="button" id="addDreamEntryBtn" class="add-entry-btn">Add New Dream</button>

                    <div class="data-table-container">
                        <table id="dreamsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>Time Frame</th>
                                    <th>Dream</th>
                                    <th>Why</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                </tbody>
                        </table>
                    </div>

                    <!-- Hidden fields for compatibility with external script -->
                    <input type="hidden" id="dreamsRecordId" name="record_id">
                    <input type="hidden" id="dreamsAgentName" name="agentName">
                    
                    <!-- Dreams data will be added dynamically as individual fields -->

                    <div class="form-actions">
                      <p id="dreamsFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                      <button type="submit" class="btn btn-primary" id="saveDreamsBtn" onclick="saveDreams(event)">Save All Dreams</button>
                    </div>
                </form>
            </div>
        </div>

        <div id="expenses-to-income-report-form" class="page-content hidden">
            <div class="form-container">
                <h2 class="page-title">Expenses to Income Report</h2>
                <p class="page-description">Log your expenses and track your income.</p>

                <button id="addExpenseEntryBtn" class="add-entry-btn">Add New Expense</button>

                <div class="data-table-container">
                    <table id="expensesTable" class="data-table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Amount</th>
                                <th>Category</th>
                                <th>Date</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            </tbody>
                    </table>
                </div>

                <div class="form-actions">
                  <p id="expensesFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                  <button type="submit" class="btn btn-primary" id="saveExpensesBtn">Save All Expenses</button>
                </div>
            </div>
        </div>

        <div id="potential-business-partners-form" class="page-content hidden">
            <div class="form-container">
                <h2 class="page-title">Potential Business Partners</h2>
                <p class="page-description">Manage your list of potential business partners.</p>

                <button id="addPartnerEntryBtn" class="add-entry-btn">Add New Partner</button>

                <div class="data-table-container">
                    <table id="partnersTable" class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            </tbody>
                    </table>
                </div>

                <div class="form-actions">
                  <p id="partnersFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                  <button type="submit" class="btn btn-primary" id="savePartnersBtn">Save All Partners</button>
                </div>
            </div>
        </div>

        <div id="potential-field-trainings-form" class="page-content hidden">
            <div class="form-container">
                <h2 class="page-title">Potential Field Trainings</h2>
                <p class="page-description">Track and manage potential field training opportunities.</p>

                <form id="clientsForm">
                    <button type="button" id="addTrainingEntryBtn" class="add-entry-btn">Add New Training</button>

                    <div class="data-table-container">
                        <table id="trainingsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>Client Name</th>
                                    <th>Date</th>
                                    <th>Type of Training</th>
                                    <th>Outcome</th>
                                    <th>Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                </tbody>
                        </table>
                    </div>

                    <!-- Hidden fields for compatibility with external script -->
                    <input type="hidden" id="clientsRecordId" name="record_id">
                    <input type="hidden" id="clientsAgentName" name="agentName">
                    
                    <!-- Clients data will be added dynamically as individual fields -->

                    <div class="form-actions">
                      <p id="clientsFormSaveStatus" class="text-sm text-gray-500 mr-auto flex items-center"></p>
                      <button type="submit" class="btn btn-primary" id="saveClientsBtn">Save All Trainings</button>
                    </div>
                </form>
            </div>
        </div>

      </div>
    </div>
  </div>

        <!-- User Management Form (Admin Only) -->
        <div id="user-management-form" class="page-content hidden">
          <div class="form-container">
            <!-- Header Section -->
            <div class="text-center mb-8">
              <h2 class="page-title text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-users-cog text-blue-600 mr-3"></i>User Management
              </h2>
              <p class="page-description text-gray-600">Create new users, change passwords, and manage existing accounts</p>
            </div>
            
            <!-- Main Content Grid -->
            <div class="grid md:grid-cols-2 gap-6 mb-6">
              
              <!-- Left Column: Create User -->
              <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl shadow-lg border border-blue-200">
                <div class="flex items-center mb-4">
                  <div class="bg-blue-600 text-white rounded-full p-2 mr-3">
                    <i class="fas fa-user-plus text-lg"></i>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-800">Create New User</h3>
                </div>
                
                <form id="createUserForm" class="space-y-4">
                  <div class="form-group">
                    <label for="newAgentName" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-user text-blue-500 mr-2"></i>Agent Name
                    </label>
                    <input type="text" id="newAgentName" name="agentName" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                  </div>
                  
                  <div class="form-group">
                    <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-lock text-blue-500 mr-2"></i>Password
                    </label>
                    <input type="password" id="newPassword" name="password" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                  </div>
                  
                  <div class="form-group">
                    <label for="newRole" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-shield-alt text-blue-500 mr-2"></i>Role
                    </label>
                    <select id="newRole" name="role" required
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                      <option value="user">User</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label for="newAvatarUrl" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-image text-blue-500 mr-2"></i>Avatar URL (Optional)
                    </label>
                    <input type="url" id="newAvatarUrl" name="avatarUrl" 
                           placeholder="https://example.com/avatar.jpg"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                  </div>
                  
                  <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <i class="fas fa-plus mr-2"></i>Create User
                  </button>
                </form>
              </div>
              
              <!-- Right Column: Change Password -->
              <div class="bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-xl shadow-lg border border-green-200">
                <div class="flex items-center mb-4">
                  <div class="bg-green-600 text-white rounded-full p-2 mr-3">
                    <i class="fas fa-key text-lg"></i>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-800">Change Password</h3>
                </div>
                
                <form id="changePasswordForm" class="space-y-4">
                  <div class="form-group">
                    <label for="targetAgentName" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-user-edit text-green-500 mr-2"></i>Select User
                    </label>
                    <select id="targetAgentName" name="targetAgentName" required
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                      <option value="">-- Select User --</option>
                      <!-- Options will be populated dynamically -->
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label for="newPasswordChange" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-lock text-green-500 mr-2"></i>New Password
                    </label>
                    <input type="password" id="newPasswordChange" name="newPassword" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                  </div>
                  
                  <div class="form-group">
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
                      <i class="fas fa-lock text-green-500 mr-2"></i>Confirm Password
                    </label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                  </div>
                  
                  <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center">
                    <i class="fas fa-key mr-2"></i>Change Password
                  </button>
                </form>
              </div>
            </div>
            
            <!-- User List Section -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
              <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-4">
                <div class="flex items-center text-white">
                  <div class="bg-white bg-opacity-20 rounded-full p-2 mr-3">
                    <i class="fas fa-users text-lg"></i>
                  </div>
                  <h3 class="text-xl font-semibold">Existing Users</h3>
                </div>
              </div>
              
              <div class="p-6">
                <div class="overflow-x-auto">
                  <table id="usersTable" class="w-full">
                    <thead>
                      <tr class="border-b-2 border-gray-200">
                        <th class="text-left py-3 px-4 font-semibold text-gray-700">
                          <i class="fas fa-user text-blue-500 mr-2"></i>Agent Name
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-700">
                          <i class="fas fa-shield-alt text-green-500 mr-2"></i>Role
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-700">
                          <i class="fas fa-clock text-orange-500 mr-2"></i>Last Updated
                        </th>
                        <th class="text-center py-3 px-4 font-semibold text-gray-700">
                          <i class="fas fa-cogs text-purple-500 mr-2"></i>Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody id="usersTableBody" class="divide-y divide-gray-200">
                      <tr>
                        <td colspan="4" class="text-center py-8 text-gray-500">
                          <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                          <p>Loading users...</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <div id="dreamEntryModal" class="modal">
    <div class="modal-content">
      <button class="modal-close-button" id="closeDreamModal">&times;</button>
      <h3 class="card-title mb-4">Dream Details</h3>
      <form id="modalDreamForm">
        <input type="hidden" id="modalDreamRecordId" name="record_id">
        <div class="form-group">
          <label for="modalTimeFrame">Time Frame:</label>
          <select id="modalTimeFrame" name="time_frame">
            <option value="3 Months">3 Months</option>
            <option value="6 Months">6 Months</option>
            <option value="1 Year">1 Year</option>
            <option value="2 Years">2 Years</option>
            <option value="3 Years">3 Years</option>
            <option value="4 Years +">4 Years +</option>
          </select>
        </div>
        <div class="form-group">
          <label for="modalDream">Dream:</label>
          <input type="text" id="modalDream" name="dream" required>
        </div>
        <div class="form-group">
          <label for="modalWhy">Why:</label>
          <textarea id="modalWhy" name="why"></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-primary" id="saveModalDreamBtn">Save Dream</button>
        </div>
      </form>
    </div>
  </div>

  <div id="expenseEntryModal" class="modal">
    <div class="modal-content">
      <button class="modal-close-button" id="closeExpenseModal">&times;</button>
      <h3 class="card-title mb-4">Expense Details</h3>
      <form id="modalExpenseForm">
        <input type="hidden" id="modalExpenseRecordId" name="record_id">
        <div class="form-group">
          <label for="modalItem">Item:</label>
          <input type="text" id="modalItem" name="item" required>
        </div>
        <div class="form-group">
          <label for="modalAmount">Amount:</label>
          <input type="text" id="modalAmount" name="amount" required>
        </div>
        <div class="form-group">
          <label for="modalCategory">Category:</label>
          <input type="text" id="modalCategory" name="category">
        </div>
        <div class="form-group">
          <label for="modalExpenseDate">Date:</label>
          <input type="date" id="modalExpenseDate" name="date" required>
        </div>
        <div class="form-group">
          <label for="modalDescription">Description:</label>
          <textarea id="modalDescription" name="description"></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-primary" id="saveModalExpenseBtn">Save Expense</button>
        </div>
      </form>
    </div>
  </div>

  <div id="partnerEntryModal" class="modal">
    <div class="modal-content">
      <button class="modal-close-button" id="closePartnerModal">&times;</button>
      <h3 class="card-title mb-4">Partner Details</h3>
      <form id="modalPartnerForm">
        <input type="hidden" id="modalPartnerRecordId" name="record_id">
        <div class="form-group">
          <label for="modalPartnerName">Name:</label>
          <input type="text" id="modalPartnerName" name="name" required>
        </div>
        <div class="form-group">
          <label for="modalPartnerContact">Contact:</label>
          <input type="text" id="modalPartnerContact" name="contact">
        </div>
        <div class="form-group">
          <label for="modalPartnerEmail">Email:</label>
          <input type="email" id="modalPartnerEmail" name="email">
        </div>
        <div class="form-group">
          <label for="modalPartnerStatus">Status:</label>
          <input type="text" id="modalPartnerStatus" name="status">
        </div>
        <div class="form-group">
          <label for="modalPartnerNotes">Notes:</label>
          <textarea id="modalPartnerNotes" name="notes"></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-primary" id="saveModalPartnerBtn">Save Partner</button>
        </div>
      </form>
    </div>
  </div>

  <div id="clientEntryModal" class="modal">
    <div class="modal-content">
      <button class="modal-close-button" id="closeClientModal">&times;</button>
      <h3 class="card-title mb-4">Field Training Details</h3>
      <form id="modalClientForm">
        <input type="hidden" id="modalClientRecordId" name="record_id">
        <div class="form-group">
          <label for="modalClientName">Client Name:</label>
          <input type="text" id="modalClientName" name="client_name" required>
        </div>
        <div class="form-group">
          <label for="modalClientDate">Date:</label>
          <input type="date" id="modalClientDate" name="date" required>
        </div>
        <div class="form-group">
          <label for="modalClientType">Type of Training:</label>
          <input type="text" id="modalClientType" name="type_of_training">
        </div>
        <div class="form-group">
          <label for="modalClientOutcome">Outcome:</label>
          <input type="text" id="modalClientOutcome" name="outcome">
        </div>
        <div class="form-group">
          <label for="modalClientNotes">Notes:</label>
          <textarea id="modalClientNotes" name="notes"></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-primary" id="saveModalClientBtn">Save Training</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Load the enhanced script with all fixes -->
  <script src="script_enhanced.js"></script>
  
  <script>
    // Dreams List functionality - GLOBAL SCOPE
    let dreamEntries = [];
    let dreamEntryCounter = 0;

    // Enhanced function to check user login status with fallback
    function checkUserLoginStatus(showDebug = false) {
      if (showDebug) {
        console.log('🔍 Checking user login status...');
        console.log('window.currentAgent:', window.currentAgent);
        console.log('typeof window.currentAgent:', typeof window.currentAgent);
      }
      
      // Check multiple possible sources for current agent
      const sources = [
        window.currentAgent,
        window.agent,
        window.userData,
        (() => {
          try {
            return localStorage.getItem('currentAgent') ? JSON.parse(localStorage.getItem('currentAgent')) : null;
          } catch (e) { return null; }
        })(),
        (() => {
          try {
            return sessionStorage.getItem('currentAgent') ? JSON.parse(sessionStorage.getItem('currentAgent')) : null;
          } catch (e) { return null; }
        })(),
        (() => {
          try {
            return localStorage.getItem('agentName') ? {agentName: localStorage.getItem('agentName')} : null;
          } catch (e) { return null; }
        })()
      ];
      
      for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        if (source && (source.agentName || source.agent)) {
          if (showDebug) console.log(`✅ Found agent in source ${i}:`, source);
          
          // Normalize the agent data
          const agentName = source.agentName || source.agent;
          if (agentName && agentName.trim()) {
            // Update window.currentAgent if it's not set properly
            if (!window.currentAgent || !window.currentAgent.agentName) {
              window.currentAgent = {
                agentName: agentName,
                ...source
              };
            }
            return true;
          }
        }
      }
      
      if (showDebug) console.log('❌ No valid agent found in any source');
      return false;
    }

    // Function to get current agent name safely
    function getCurrentAgentName() {
      if (checkUserLoginStatus()) {
        return window.currentAgent.agentName || window.currentAgent.agent || 'Unknown';
      }
      return null;
    }

    // Function to normalize time frame values from different sources
    function normalizeTimeFrame(value) {
      if (!value) return '';
      
      const str = value.toString().trim();
      
      // Create mapping for various formats
      const mappings = {
        '1year': '1 Year',
        '1 year': '1 Year',
        '1Year': '1 Year',
        '1-year': '1 Year',
        'one year': '1 Year',
        
        '3years': '3 Years',
        '3 years': '3 Years', 
        '3Years': '3 Years',
        '3-years': '3 Years',
        'three years': '3 Years',
        
        '5years': '5 Years',
        '5 years': '5 Years',
        '5Years': '5 Years', 
        '5-years': '5 Years',
        'five years': '5 Years',
        
        '10years': '10 Years',
        '10 years': '10 Years',
        '10Years': '10 Years',
        '10-years': '10 Years', 
        'ten years': '10 Years',
        
        'lifetime': 'Lifetime',
        'Lifetime': 'Lifetime',
        'LIFETIME': 'Lifetime',
        'life time': 'Lifetime',
        'life-time': 'Lifetime'
      };
      
      // Check exact matches first
      if (mappings[str]) {
        return mappings[str];
      }
      
      // Check case-insensitive matches
      const lowerStr = str.toLowerCase();
      if (mappings[lowerStr]) {
        return mappings[lowerStr];
      }
      
      // If no mapping found, return original value
      return str;
    }

    function addDreamEntry() {
      console.log('➕ Adding new dream entry...');
      
      // Simple check - just try to get agent name
      const agentName = window.currentAgent?.agentName || 'admin';
      console.log('Agent name:', agentName);
      
      dreamEntryCounter++;
      const newEntry = {
        id: dreamEntryCounter,
        record_id: `${agentName}_dream_${dreamEntryCounter}_${Date.now()}`,
        timeFrame: '',
        dream: '',
        why: ''
      };
      
      dreamEntries.push(newEntry);
      console.log('📝 New entry added:', newEntry);
      
      renderDreamEntry(newEntry);
      
      console.log('✅ Dream entry added successfully');
    }

    function renderDreamEntry(entry) {
      console.log('🎨 Rendering dream entry:', entry);
      
      const tbody = document.querySelector('#dreamsTable tbody');
      if (!tbody) {
        console.error('❌ Dreams table tbody not found');
        return;
      }
      
      const row = document.createElement('tr');
      row.setAttribute('data-entry-id', entry.id);
      
      row.innerHTML = `
        <td>
          <select class="form-control dream-time-frame">
            <option value="">Select Time Frame</option>
            <option value="1 Year" ${entry.timeFrame === '1 Year' ? 'selected' : ''}>1 Year</option>
            <option value="3 Years" ${entry.timeFrame === '3 Years' ? 'selected' : ''}>3 Years</option>
            <option value="5 Years" ${entry.timeFrame === '5 Years' ? 'selected' : ''}>5 Years</option>
            <option value="10 Years" ${entry.timeFrame === '10 Years' ? 'selected' : ''}>10 Years</option>
            <option value="Lifetime" ${entry.timeFrame === 'Lifetime' ? 'selected' : ''}>Lifetime</option>
          </select>
        </td>
        <td>
          <textarea class="form-control dream-description" rows="3" placeholder="Describe your dream">${entry.dream}</textarea>
        </td>
        <td>
          <textarea class="form-control dream-why" rows="3" placeholder="Why is this important?">${entry.why}</textarea>
        </td>
        <td class="table-actions">
          <button type="button" onclick="removeDreamEntry(${entry.id})" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      `;
      
      tbody.appendChild(row);
      
      // Simple event listeners for auto-save
      row.querySelectorAll('input, textarea, select').forEach(input => {
        input.addEventListener('input', function() {
          const entryId = parseInt(row.getAttribute('data-entry-id'));
          const entryIndex = dreamEntries.findIndex(e => e.id === entryId);
          if (entryIndex !== -1) {
            dreamEntries[entryIndex].timeFrame = row.querySelector('.dream-time-frame').value;
            dreamEntries[entryIndex].dream = row.querySelector('.dream-description').value;
            dreamEntries[entryIndex].why = row.querySelector('.dream-why').value;
            updateDreamsFormData();
          }
        });
      });
      
      console.log('✅ Dream entry rendered:', entry.id);
    }

    // Enhanced function to set up event listeners for dream entries
    function setupDreamEntryListeners(row) {
      const entryId = parseInt(row.getAttribute('data-entry-id'));
      
      row.querySelectorAll('input, textarea, select').forEach(input => {
        // Real-time validation and character counting
        input.addEventListener('input', function() {
          validateDreamInput(input);
          updateCharacterCount(input);
          updateDreamEntryData(entryId, row);
        });

        // Blur validation
        input.addEventListener('blur', function() {
          validateDreamInput(input);
        });

        // Focus enhancement
        input.addEventListener('focus', function() {
          input.classList.remove('border-red-500');
        });
      });
    }

    // Function to validate individual dream inputs
    function validateDreamInput(input) {
      const value = input.value.trim();
      let isValid = true;
      
      // Remove existing styling
      input.classList.remove('border-red-500', 'border-green-500');
      
      // Check if required field is empty
      if (input.hasAttribute('required') && !value) {
        input.classList.add('border-red-500');
        isValid = false;
      }
      // Check minimum length for textareas
      else if (input.tagName === 'TEXTAREA' && value && value.length < 10) {
        input.classList.add('border-red-500');
        isValid = false;
      }
      // Check maximum length
      else if (input.hasAttribute('maxlength') && value.length > parseInt(input.getAttribute('maxlength'))) {
        input.classList.add('border-red-500');
        isValid = false;
      }
      // Valid input
      else if (value) {
        input.classList.add('border-green-500');
      }
      
      return isValid;
    }

    // Function to update character count
    function updateCharacterCount(input) {
      const row = input.closest('tr');
      if (!row) return;
      
      const charCountElement = row.querySelector('.char-count');
      if (charCountElement) {
        const currentLength = input.value.length;
        const maxLength = input.getAttribute('maxlength') || 500;
        charCountElement.textContent = currentLength;
        
        // Change color based on usage
        if (currentLength > maxLength * 0.9) {
          charCountElement.classList.add('text-red-500');
          charCountElement.classList.remove('text-yellow-500', 'text-gray-500');
        } else if (currentLength > maxLength * 0.7) {
          charCountElement.classList.add('text-yellow-500');
          charCountElement.classList.remove('text-red-500', 'text-gray-500');
        } else {
          charCountElement.classList.add('text-gray-500');
          charCountElement.classList.remove('text-red-500', 'text-yellow-500');
        }
      }
    }

    // Function to update dream entry data
    function updateDreamEntryData(entryId, row) {
      const entryIndex = dreamEntries.findIndex(e => e.id === entryId);
      if (entryIndex !== -1) {
        dreamEntries[entryIndex].timeFrame = row.querySelector('.dream-time-frame').value;
        dreamEntries[entryIndex].dream = row.querySelector('.dream-description').value;
        dreamEntries[entryIndex].why = row.querySelector('.dream-why').value;
        
        // Update the form data structure
        updateDreamsFormData();
        
        // Debounced auto-save
        clearTimeout(window.dreamAutoSaveTimeout);
        window.dreamAutoSaveTimeout = setTimeout(() => {
          if (window.autoSaveEnabled !== false) {
            console.log('🔄 Auto-saving dreams...');
            saveDreams();
          }
        }, 3000);
      }
    }

    // New function to edit a dream entry (focus and highlight)
    function editDreamEntry(entryId) {
      console.log('✏️ Editing dream entry:', entryId);
      
      const row = document.querySelector(`tr[data-entry-id="${entryId}"]`);
      if (!row) {
        console.error('❌ Entry row not found');
        return;
      }

      // Highlight the row
      row.classList.add('bg-blue-50', 'border-blue-300');
      
      // Focus on the first input
      const firstInput = row.querySelector('.dream-time-frame');
      if (firstInput) {
        firstInput.focus();
      }
      
      // Remove highlight after 3 seconds
      setTimeout(() => {
        row.classList.remove('bg-blue-50', 'border-blue-300');
      }, 3000);
      
      if (typeof window.toastManager !== 'undefined') {
        window.toastManager.show('Edit mode activated for this dream', 'info');
      }
    }

    // Enhanced function to duplicate a dream entry
    function duplicateDreamEntry(entryId) {
      console.log('📋 Duplicating dream entry:', entryId);
      
      const sourceEntry = dreamEntries.find(e => e.id === entryId);
      if (!sourceEntry) {
        console.error('❌ Source entry not found for duplication');
        return;
      }

      dreamEntryCounter++;
      const newEntry = {
        id: dreamEntryCounter,
        record_id: `${window.currentAgent?.agentName}_dream_${dreamEntryCounter}_${Date.now()}`,
        timeFrame: sourceEntry.timeFrame,
        dream: sourceEntry.dream + ' (Copy)',
        why: sourceEntry.why
      };
      
      dreamEntries.push(newEntry);
      renderDreamEntry(newEntry);
      updateDreamsFormData();
      
      // Show success feedback
      if (typeof window.toastManager !== 'undefined') {
        window.toastManager.show('Dream entry duplicated successfully!', 'success');
      }
      
      console.log('✅ Dream entry duplicated successfully');
    }

    function updateDreamsFormData() {
      // Remove old hidden inputs
      document.querySelectorAll('#dreamsForm input[name^="dreams"]').forEach(input => {
        if (input.type === 'hidden') {
          input.remove();
        }
      });
      
      // Create new hidden inputs for form submission
      const form = document.getElementById('dreamsForm');
      if (!form) return;
      
      // Instead of nested arrays, create a JSON string of all dreams
      // and individual fields for the first dream (for compatibility)
      if (dreamEntries.length > 0) {
        // Create JSON data for all dreams
        const dreamsJsonInput = document.createElement('input');
        dreamsJsonInput.type = 'hidden';
        dreamsJsonInput.name = 'dreams_data';
        dreamsJsonInput.value = JSON.stringify(dreamEntries);
        form.appendChild(dreamsJsonInput);
        
        // Also create individual fields for the first dream (for Google Sheet columns)
        const firstDream = dreamEntries[0];
        if (firstDream) {
          const timeFrameInput = document.createElement('input');
          timeFrameInput.type = 'hidden';
          timeFrameInput.name = 'time_frame';
          timeFrameInput.value = firstDream.timeFrame || '';
          form.appendChild(timeFrameInput);
          
          const dreamInput = document.createElement('input');
          dreamInput.type = 'hidden';
          dreamInput.name = 'dream';
          dreamInput.value = firstDream.dream || '';
          form.appendChild(dreamInput);
          
          const whyInput = document.createElement('input');
          whyInput.type = 'hidden';
          whyInput.name = 'why';
          whyInput.value = firstDream.why || '';
          form.appendChild(whyInput);
        }
        
        // Add count of total dreams
        const countInput = document.createElement('input');
        countInput.type = 'hidden';
        countInput.name = 'dreams_count';
        countInput.value = dreamEntries.length.toString();
        form.appendChild(countInput);
      }
    }

    function removeDreamEntry(entryId) {
      console.log('🗑️ Removing dream entry:', entryId);
      
      // Find the entry to get its details for confirmation
      const entryToRemove = dreamEntries.find(e => e.id === entryId);
      if (!entryToRemove) {
        console.error('❌ Entry not found for removal');
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show('Entry not found!', 'error');
        }
        return;
      }

      // Show confirmation dialog
      const dreamPreview = entryToRemove.dream.length > 50 
        ? entryToRemove.dream.substring(0, 50) + '...' 
        : entryToRemove.dream;
      
      const confirmMessage = `Are you sure you want to delete this dream?\n\n"${dreamPreview}"`;
      
      if (!confirm(confirmMessage)) {
        console.log('❌ User cancelled deletion');
        return;
      }

      // Get the row for animation
      const row = document.querySelector(`tr[data-entry-id="${entryId}"]`);
      
      if (row) {
        // Add fade-out animation
        row.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        row.style.opacity = '0';
        row.style.transform = 'translateX(-20px)';
        
        // Remove after animation
        setTimeout(() => {
          row.remove();
          console.log('✅ Row removed from DOM');
        }, 300);
      }

      // Remove from array
      const originalLength = dreamEntries.length;
      dreamEntries = dreamEntries.filter(entry => entry.id !== entryId);
      
      if (dreamEntries.length < originalLength) {
        console.log('✅ Entry removed from array');
        
        // Update form data
        updateDreamsFormData();
        
        // Show success feedback
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show('Dream entry deleted successfully!', 'success');
        }
        
        // Auto-save after removal
        clearTimeout(window.dreamAutoSaveTimeout);
        window.dreamAutoSaveTimeout = setTimeout(() => {
          console.log('🔄 Auto-saving after deletion...');
          saveDreams();
        }, 1000);
        
      } else {
        console.error('❌ Failed to remove entry from array');
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show('Failed to delete entry!', 'error');
        }
      }
    }

    async function saveDreams(event) {
      if (event) {
        event.preventDefault();
      }
      
      console.log('=== Starting Dreams Save Process ===');
      
      // Check if user is logged in - use same logic as add function
      const agentName = window.currentAgent?.agentName || 'admin';
      console.log('💾 Save agent name:', agentName);

      console.log('✅ User logged in:', agentName);

      // Validate all dream entries
      const validationResult = validateDreamEntries();
      if (!validationResult.isValid) {
        console.log('❌ Validation failed:', validationResult.errors);
        showDreamsStatus('Please fix validation errors', 'error');
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show('Validation errors: ' + validationResult.errors.join(', '), 'error');
        }
        return;
      }

      console.log('✅ Validation passed');

      // Update entries from current form values
      updateDreamEntriesFromForm();
      
      // Show loading status
      showDreamsStatus('Saving dreams...', 'loading');
      
      try {
        // Prepare data for bulk update
        const bulkUpdateData = {
          action: 'bulkUpdateEntries',
          formId: 'dreamsForm',
          sheetName: 'Dreams List',
          agent: agentName,
          entries: dreamEntries.map(entry => ({
            record_id: entry.record_id || `${window.currentAgent.agentName}_dream_${entry.id}`,
            time_frame: entry.timeFrame || '',
            dream: entry.dream || '',
            why: entry.why || ''
          }))
        };

        console.log('📤 Sending bulk update data:', bulkUpdateData);

        // Use the bulk update API endpoint
        const scriptId = window.scriptId || 'AKfycbxD0wWtSaY2Dq4Ugz5s54nc155RLc6Tf47EkpBYSdyMmhZdZZeM-ta032dKVNLNl6uphg';
        const url = `https://script.google.com/macros/s/${scriptId}/exec?action=bulkUpdateEntries&data=${encodeURIComponent(JSON.stringify(bulkUpdateData))}`;
        
        console.log('📡 Making API call to:', url.substring(0, 100) + '...');
        
        // Make the API call
        const response = await makeApiCall(url);
        
        console.log('📨 API Response:', response);

        if (response && response.status === 'success') {
          console.log('✅ Dreams saved successfully');
          showDreamsStatus('Dreams saved successfully!', 'success');
          if (typeof window.toastManager !== 'undefined') {
            window.toastManager.show('Dreams saved successfully!', 'success');
          }
          
          // Update the form data to reflect saved state
          updateDreamsFormData();
          
        } else {
          throw new Error(response?.message || 'Unknown error occurred');
        }

      } catch (error) {
        console.error('❌ Error saving dreams:', error);
        showDreamsStatus('Error saving dreams', 'error');
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show('Error saving dreams: ' + error.message, 'error');
        }
      }
    }

    // Enhanced status display function
    function showDreamsStatus(message, type = 'info') {
      const statusElement = document.getElementById('dreamsFormSaveStatus');
      if (!statusElement) return;

      const icons = {
        success: '<i class="fas fa-check text-green-500 mr-2"></i>',
        error: '<i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>',
        loading: '<i class="fas fa-spinner fa-spin text-blue-500 mr-2"></i>',
        info: '<i class="fas fa-info-circle text-blue-500 mr-2"></i>'
      };

      const colors = {
        success: 'text-green-600',
        error: 'text-red-600',
        loading: 'text-blue-600',
        info: 'text-blue-600'
      };

      statusElement.innerHTML = `<span class="${colors[type]}">${icons[type]}${message}</span>`;
      statusElement.style.opacity = '1';
      
      // Auto-hide after 5 seconds (except for loading)
      if (type !== 'loading') {
        setTimeout(() => {
          statusElement.style.opacity = '0';
          setTimeout(() => {
            statusElement.innerHTML = '';
          }, 300);
        }, 5000);
      }
    }

    // Enhanced validation function for dream entries
    function validateDreamEntries() {
      const errors = [];
      let isValid = true;

      dreamEntries.forEach((entry, index) => {
        const entryNum = index + 1;
        
        // Validate time frame
        if (!entry.timeFrame || entry.timeFrame.trim() === '') {
          errors.push(`Entry ${entryNum}: Time Frame is required`);
          isValid = false;
        }

        // Validate dream description
        if (!entry.dream || entry.dream.trim() === '') {
          errors.push(`Entry ${entryNum}: Dream description is required`);
          isValid = false;
        } else if (entry.dream.length < 10) {
          errors.push(`Entry ${entryNum}: Dream description should be at least 10 characters`);
          isValid = false;
        }

        // Validate why
        if (!entry.why || entry.why.trim() === '') {
          errors.push(`Entry ${entryNum}: Why is required`);
          isValid = false;
        } else if (entry.why.length < 10) {
          errors.push(`Entry ${entryNum}: Why should be at least 10 characters`);
          isValid = false;
        }
      });

      // Check if at least one entry exists
      if (dreamEntries.length === 0) {
        errors.push('At least one dream entry is required');
        isValid = false;
      }

      return { isValid, errors };
    }

    // Enhanced function to update dream entries from form
    function updateDreamEntriesFromForm() {
      console.log('📝 Updating dream entries from form...');
      
      document.querySelectorAll('#dreamsTable tbody tr').forEach(row => {
        const entryId = parseInt(row.getAttribute('data-entry-id'));
        const entryIndex = dreamEntries.findIndex(e => e.id === entryId);
        
        if (entryIndex !== -1) {
          const timeFrameInput = row.querySelector('.dream-time-frame');
          const dreamInput = row.querySelector('.dream-description');
          const whyInput = row.querySelector('.dream-why');

          if (timeFrameInput && dreamInput && whyInput) {
            dreamEntries[entryIndex].timeFrame = timeFrameInput.value.trim();
            dreamEntries[entryIndex].dream = dreamInput.value.trim();
            dreamEntries[entryIndex].why = whyInput.value.trim();
            
            // Ensure record_id is set
            if (!dreamEntries[entryIndex].record_id) {
              dreamEntries[entryIndex].record_id = `${window.currentAgent?.agentName}_dream_${entryId}`;
            }
            
            console.log(`📝 Updated entry ${entryId}:`, dreamEntries[entryIndex]);
          }
        }
      });
      
      console.log('📝 All entries updated:', dreamEntries);
    }

    // Enhanced debug function to test dreams form
    window.testDreamsForm = function() {
      console.log('=== 🧪 Dreams Form Debug Info ===');
      console.log('Dreams entries:', dreamEntries);
      console.log('Dreams entry counter:', dreamEntryCounter);
      console.log('Current agent:', window.currentAgent);
      console.log('Save function available:', typeof window.saveFormData);
      console.log('Toast manager available:', typeof window.toastManager);
      
      // Test login status
      const isLoggedIn = checkUserLoginStatus();
      console.log('Login status:', isLoggedIn);
      
      // Check form elements
      const form = document.getElementById('dreamsForm');
      const addBtn = document.getElementById('addDreamEntryBtn');
      const table = document.getElementById('dreamsTable');
      const tbody = table?.querySelector('tbody');
      
      console.log('Form elements:', {
        form: !!form,
        addBtn: !!addBtn,
        table: !!table,
        tbody: !!tbody,
        tbodyRows: tbody?.children.length || 0
      });
      
      if (form) {
        // First update the form data to see the latest values
        updateDreamsFormData();
        
        const formData = new FormData(form);
        console.log('Current form data being sent to server:');
        for (let [key, value] of formData.entries()) {
          console.log(`  ${key}: ${value}`);
        }
      } else {
        console.log('❌ Dreams form not found');
      }
      
      // Test Add Dream Entry function
      console.log('=== Testing Add Dream Entry ===');
      try {
        addDreamEntry();
        console.log('✅ Add Dream Entry function executed successfully');
      } catch (error) {
        console.error('❌ Error in Add Dream Entry:', error);
      }
    };

    // Debug function to test individual features
    window.testDreamsFeature = function(feature) {
      console.log(`🧪 Testing Dreams feature: ${feature}`);
      
      switch (feature) {
        case 'login':
          return checkUserLoginStatus();
        case 'add':
          addDreamEntry();
          break;
        case 'save':
          saveDreams();
          break;
        case 'load':
          loadDreamsData();
          break;
        case 'init':
          initializeDreamsList();
          break;
        default:
          console.log('Available features: login, add, save, load, init');
      }
    };
    
    // Simple test functions
    window.testAdd = function() {
      console.log('Testing Add Dream Entry...');
      addDreamEntry();
    };
    
    window.testSave = function() {
      console.log('Testing Save Dreams...');
      saveDreams();
    };
    
    window.debugDreams = function() {
      console.log('=== Dreams Debug Info ===');
      console.log('dreamEntries:', dreamEntries);
      console.log('dreamEntryCounter:', dreamEntryCounter);
      console.log('currentAgent:', window.currentAgent);
      console.log('Add button exists:', !!document.getElementById('addDreamEntryBtn'));
      console.log('Save button exists:', !!document.getElementById('saveDreamsBtn'));
      console.log('Dreams form exists:', !!document.getElementById('dreamsForm'));
      console.log('Dreams table exists:', !!document.getElementById('dreamsTable'));
    };
    
    // Test function to add multiple dreams for testing
    window.testMultipleDreams = function() {
      console.log('🧪 Testing multiple dreams...');
      
      // Clear existing
      dreamEntries = [];
      dreamEntryCounter = 0;
      const tbody = document.querySelector('#dreamsTable tbody');
      if (tbody) tbody.innerHTML = '';
      
      // Add test data
      const testDreams = [
        {timeFrame: '1 Year', dream: 'Learn a new skill', why: 'Personal growth'},
        {timeFrame: '3 Years', dream: 'Buy a house', why: 'Stability and investment'},
        {timeFrame: '5 Years', dream: 'Start own business', why: 'Financial independence'},
        {timeFrame: 'Lifetime', dream: 'Travel the world', why: 'Life experiences'}
      ];
      
      testDreams.forEach((testDream, index) => {
        dreamEntryCounter++;
        const entry = {
          id: dreamEntryCounter,
          record_id: `admin_dream_${dreamEntryCounter}_${Date.now()}_test`,
          timeFrame: testDream.timeFrame,
          dream: testDream.dream,
          why: testDream.why
        };
        
        dreamEntries.push(entry);
        renderDreamEntry(entry);
        console.log(`✅ Added test dream ${index + 1}:`, entry);
      });
      
      console.log(`🎯 Added ${testDreams.length} test dreams`);
    };

    // Debug function to test dreams form
    window.testDreamsForm = function() {
      console.log('=== Dreams Form Debug Info ===');
      console.log('Dreams entries:', dreamEntries);
      console.log('Current agent:', window.currentAgent);
      console.log('Save function available:', typeof window.saveFormData);
      console.log('Toast manager available:', typeof window.toastManager);
      
      const form = document.getElementById('dreamsForm');
      if (form) {
        // First update the form data to see the latest values
        updateDreamsFormData();
        
        const formData = new FormData(form);
        console.log('Current form data being sent to server:');
        for (let [key, value] of formData.entries()) {
          console.log(`  ${key}: ${value}`);
        }
        
        console.log('Dreams entries (raw data):');
        dreamEntries.forEach((entry, index) => {
          console.log(`  Dream ${index + 1}:`, entry);
        });
      } else {
        console.log('Dreams form not found!');
      }
      
      // Show in UI
      const statusElement = document.getElementById('dreamsFormSaveStatus');
      if (statusElement) {
        statusElement.innerHTML = '<i class="fas fa-info-circle text-blue-500 mr-2"></i>Debug info in console';
        setTimeout(() => {
          statusElement.innerHTML = '';
        }, 3000);
      }
    };

    // Enhanced functionality for the main page
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 WFG Enhanced System - Main Page Ready!');
    
    // Debug navigation
    window.debugNavigation = function() {
      console.log('🔍 Debugging navigation...');
      console.log('Available functions:');
      console.log('- navigateTo:', typeof navigateTo);
      console.log('- login:', typeof login);
      console.log('- currentAgent:', currentAgent);
      
      console.log('Available forms:');
      const forms = document.querySelectorAll('.page-content');
      forms.forEach(form => {
        const isHidden = form.classList.contains('hidden');
        const computedStyle = window.getComputedStyle(form);
        console.log(`- ${form.id}:`);
        console.log(`  - Hidden class: ${isHidden}`);
        console.log(`  - Display: ${computedStyle.display}`);
        console.log(`  - Opacity: ${computedStyle.opacity}`);
        console.log(`  - Visibility: ${computedStyle.visibility}`);
      });
    };
    
    // Force show form using the SAME method as showAllForms
    window.forceShowForm = function(formId) {
      console.log(`🔧 FORCE showing form using showAllForms method: ${formId}`);
      const form = document.getElementById(formId);
      if (form) {
        // Hide all forms using showAllForms method
        document.querySelectorAll('.page-content').forEach(page => {
          page.classList.add('hidden');
          page.style.display = 'none';
          page.style.opacity = '0';
          page.style.visibility = 'hidden';
        });
        
        // Show target form using showAllForms method
        form.classList.remove('hidden');
        form.style.display = 'block';
        form.style.opacity = '1';
        form.style.visibility = 'visible';
        form.style.position = 'relative';
        form.style.marginBottom = '20px';
        form.style.border = '2px solid red'; // Red border to distinguish from showAllForms
        form.style.padding = '10px';
        form.style.backgroundColor = 'white';
        
        console.log(`✅ FORCE: Form ${formId} displayed with red border`);
        console.log(`Form element:`, form);
      } else {
        console.error(`❌ Form ${formId} not found`);
      }
    };
    
    // Test all forms visibility
    window.testAllForms = function() {
      console.log('🔍 Testing all forms...');
      const forms = document.querySelectorAll('.page-content');
      forms.forEach(form => {
        const styles = window.getComputedStyle(form);
        console.log(`Form ${form.id}:`, {
          display: styles.display,
          opacity: styles.opacity,
          visibility: styles.visibility,
          position: styles.position,
          zIndex: styles.zIndex,
          width: styles.width,
          height: styles.height,
          top: styles.top,
          left: styles.left
        });
      });
    };
    
    // Application is now clean and ready for production
    
    // Fix User Management positioning - No longer needed, using default layout
    function fixUserManagementPosition() {
      // User Management now uses the same layout as other forms
      // No custom positioning needed
      console.log('✅ User Management using standard form layout');
    }
    
    // Watch for sidebar toggle
    const menuToggle = document.querySelector('.menu-toggle');
    if (menuToggle) {
      menuToggle.addEventListener('click', function() {
        setTimeout(fixUserManagementPosition, 100);
      });
    }
    
    // Fix position when page loads
    setTimeout(fixUserManagementPosition, 500);
    
    // Debug function to test positioning
    window.testUserMgmtPosition = function() {
      const userMgmtForm = document.getElementById('user-management-form');
      const formContainer = userMgmtForm?.querySelector('.form-container');
      const sidebar = document.querySelector('.sidebar');
      const mainContent = document.getElementById('mainContent');
      
      console.log('🔍 User Management Positioning Debug:');
      console.log('User Mgmt Form exists:', !!userMgmtForm);
      console.log('Form Container exists:', !!formContainer);
      console.log('Sidebar exists:', !!sidebar);
      
      if (userMgmtForm && formContainer) {
        const formRect = userMgmtForm.getBoundingClientRect();
        const containerRect = formContainer.getBoundingClientRect();
        const formStyle = window.getComputedStyle(userMgmtForm);
        const containerStyle = window.getComputedStyle(formContainer);
        
        console.log('Form Position:', {
          left: formRect.left,
          width: formRect.width,
          paddingLeft: formStyle.paddingLeft,
          paddingRight: formStyle.paddingRight
        });
        
        console.log('Container Position:', {
          left: containerRect.left,
          width: containerRect.width,
          marginLeft: containerStyle.marginLeft,
          marginRight: containerStyle.marginRight,
          maxWidth: containerStyle.maxWidth
        });
        
        if (sidebar) {
          const sidebarRect = sidebar.getBoundingClientRect();
          console.log('Sidebar:', {
            width: sidebarRect.width,
            hidden: sidebar.classList.contains('hidden')
          });
          
          // Calculate distances
          const distanceFromSidebar = containerRect.left - sidebarRect.right;
          const distanceFromRightEdge = window.innerWidth - containerRect.right;
          
          console.log('📏 Spacing:');
          console.log('Distance from sidebar:', distanceFromSidebar + 'px');
          console.log('Distance from right edge:', distanceFromRightEdge + 'px');
          console.log('Difference:', Math.abs(distanceFromSidebar - distanceFromRightEdge) + 'px');
        }
      }
      
      if (mainContent) {
        const mainStyle = window.getComputedStyle(mainContent);
        console.log('Main Content:', {
          marginLeft: mainStyle.marginLeft,
          paddingLeft: mainStyle.paddingLeft,
          paddingRight: mainStyle.paddingRight
        });
      }
    };
    
    // Add menu navigation functionality
    function showPage(pageId) {
      // Hide all page content
      document.querySelectorAll('.page-content').forEach(page => {
        page.classList.add('hidden');
      });
      
      // Show the selected page
      const targetPage = document.getElementById(pageId + 'PageContent') || document.getElementById(pageId + '-form');
      if (targetPage) {
        targetPage.classList.remove('hidden');
      }
      
      // Update active menu item
      document.querySelectorAll('.sidebar-menu a').forEach(link => {
        link.classList.remove('active');
      });
      document.querySelector(`[data-page="${pageId}"]`)?.classList.add('active');
      
      // Initialize Dreams List when showing Dreams List page
      if (pageId === 'dreams-list') {
        console.log('🎯 Dreams List page shown, loading data...');
        setTimeout(() => {
          loadDreamsData();
        }, 500);
      } else if (pageId === 'expenses-to-income-report') {
        console.log('Expenses Report page shown, loading data...');
        setTimeout(() => {
          loadExpensesData();
        }, 500);
      } else if (pageId === 'potential-business-partners') {
        console.log('Business Partners page shown, loading data...');
        setTimeout(() => {
          loadPartnersData();
        }, 500);
      } else if (pageId === 'potential-field-trainings') {
        console.log('Field Trainings page shown, loading data...');
        setTimeout(() => {
          loadClientsData();
        }, 500);
      }
    }
    
    // Add click event listeners to menu items
    document.querySelectorAll('.sidebar-menu a[data-page]').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const pageId = this.getAttribute('data-page');
        showPage(pageId);
      });
    });
    
    // Show dashboard by default on page load
    showPage('dashboard');

    // Enhanced DOM-ready event listener setup for Dreams List
    let dreamsListenersSetup = false; // Prevent duplicate setup
    
    function setupDreamsEventListeners() {
      if (dreamsListenersSetup) {
        console.log('🎯 Dreams List event listeners already set up, skipping...');
        return;
      }
      
      console.log('🎯 Setting up Dreams List event listeners...');
      
      const addDreamBtn = document.getElementById('addDreamEntryBtn');
      const dreamsForm = document.getElementById('dreamsForm');
      
      if (addDreamBtn) {
        addDreamBtn.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          console.log('🔘 Add Dream Entry button clicked');
          addDreamEntry();
        });
        console.log('✅ Add Dream Entry button listener attached');
      } else {
        console.error('❌ Add Dream Entry button not found');
      }
      
      if (dreamsForm) {
        dreamsForm.addEventListener('submit', function(e) {
          e.preventDefault();
          e.stopPropagation();
          console.log('📝 Dreams form submitted');
          saveDreams(e);
        });
        console.log('✅ Dreams form listener attached');
      } else {
        console.error('❌ Dreams form not found');
      }
      
      dreamsListenersSetup = true; // Mark as set up
      console.log('✅ Dreams List event listeners setup complete');
    }

    // Setup event listeners when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', setupDreamsEventListeners);
    } else {
      setupDreamsEventListeners();
    }
    
    // Also setup when Dreams page is shown (fallback)
    window.setupDreamsListeners = setupDreamsEventListeners;
    
    // Wait for external script to load before setting up dreams
    function waitForExternalScript() {
      if (typeof window.saveFormData === 'function' && typeof window.currentAgent !== 'undefined') {
        console.log('External script loaded successfully');
        // Load data based on current page
        if (currentPage === 'dreams-list-form' || document.getElementById('dreams-list-form')?.style.display !== 'none') {
          console.log('🎯 Dreams List page is active, initializing...');
          initializeDreamsList();
        } else if (currentPage === 'expenses-to-income-report-form' || document.getElementById('expenses-to-income-report-form')?.style.display !== 'none') {
          loadExpensesData();
        } else if (currentPage === 'potential-business-partners-form' || document.getElementById('potential-business-partners-form')?.style.display !== 'none') {
          loadPartnersData();
        } else if (currentPage === 'potential-field-trainings-form' || document.getElementById('potential-field-trainings-form')?.style.display !== 'none') {
          loadClientsData();
        }
      } else {
        console.log('Waiting for external script to load...');
        setTimeout(waitForExternalScript, 500);
      }
    }
    
    // Enhanced function to initialize Dreams List
    function initializeDreamsList() {
      console.log('🚀 Initializing Dreams List...');
      
      // Reset status
      showDreamsStatus('Initializing...', 'loading');
      
      // Check if user is logged in
      const isLoggedIn = checkUserLoginStatus();
      if (!isLoggedIn) {
        console.log('❌ User not logged in, providing guest experience');
        showDreamsStatus('Please login to save your dreams', 'info');
        
        // Clear any existing entries and add one empty entry for guest users
        dreamEntries = [];
        dreamEntryCounter = 0;
        const tbody = document.querySelector('#dreamsTable tbody');
        if (tbody) {
          tbody.innerHTML = '';
        }
        
        // Add one empty entry so guest users can still explore the interface
        setTimeout(() => {
          addDreamEntry();
          showDreamsStatus('You can explore the interface, but please login to save data', 'info');
        }, 500);
        
        return;
      }
      
      console.log('✅ User is logged in, loading data');
      loadDreamsData();
    }

    // Function to load dreams data
    function loadDreamsData() {
      console.log('📥 Loading dreams data...');
      
      if (typeof window.loadFormData === 'function') {
        console.log('✅ Loading dreams data using external function...');
        window.loadFormData('dreamsForm');
      } else {
        console.log('⚠️ External load function not available, trying alternative...');
        // Try the enhanced load function
        loadDreamsFromSheets();
      }
    }
    
    // Function to load expenses data
    function loadExpensesData() {
      if (typeof window.loadFormData === 'function') {
        console.log('Loading expenses data...');
        window.loadFormData('expensesForm');
      } else {
        console.log('Load function not available yet');
      }
    }
    
    // Function to load partners data
    function loadPartnersData() {
      if (typeof window.loadFormData === 'function') {
        console.log('Loading partners data...');
        window.loadFormData('partnersForm');
      } else {
        console.log('Load function not available yet');
      }
    }
    
    // Function to load clients data
    function loadClientsData() {
      if (typeof window.loadFormData === 'function') {
        console.log('Loading clients data...');
        window.loadFormData('clientsForm');
      } else {
        console.log('Load function not available yet');
      }
    }
    
    // Enhanced function to load dreams from Google Sheets
    async function loadDreamsFromSheets() {
      console.log('📥 Loading dreams from Google Sheets...');
      
      if (!window.currentAgent || !window.currentAgent.agentName) {
        console.log('❌ No current agent - cannot load dreams');
        return;
      }

      showDreamsStatus('Loading dreams...', 'loading');

      try {
        const scriptId = window.scriptId || 'AKfycbxD0wWtSaY2Dq4Ugz5s54nc155RLc6Tf47EkpBYSdyMmhZdZZeM-ta032dKVNLNl6uphg';
        const url = `https://script.google.com/macros/s/${scriptId}/exec?action=getFormData&formId=dreamsForm&agentName=${encodeURIComponent(window.currentAgent.agentName)}`;
        
        console.log('📡 Making API call to load dreams');
        const response = await makeApiCall(url);
        
        console.log('📨 Load dreams response:', response);

        if (response && response.entries) {
          await populateDreamsForm(response);
          showDreamsStatus('Dreams loaded successfully!', 'success');
          if (typeof window.toastManager !== 'undefined') {
            window.toastManager.show(`Loaded ${response.entries.length} dream entries`, 'success');
          }
        } else {
          console.log('No existing dreams found, starting fresh');
          showDreamsStatus('No existing dreams found', 'info');
          // Add one empty entry for new users
          if (dreamEntries.length === 0) {
            addDreamEntry();
          }
        }

      } catch (error) {
        console.error('❌ Error loading dreams:', error);
        showDreamsStatus('Error loading dreams', 'error');
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show('Error loading dreams: ' + error.message, 'error');
        }
        
        // Add empty entry on error so user can still work
        if (dreamEntries.length === 0) {
          addDreamEntry();
        }
      }
    }

    // Enhanced function to populate dreams form with better validation
    window.populateDreamsForm = function(data) {
      console.log('📝 Populating dreams form with data:', data);
      console.log('📊 Data entries length:', data.entries ? data.entries.length : 'No entries');
      console.log('📊 All entries:', data.entries);
      
      // Clear existing entries
      dreamEntries = [];
      dreamEntryCounter = 0;
      const tbody = document.querySelector('#dreamsTable tbody');
      if (tbody) {
        tbody.innerHTML = '';
      }
      
      // Handle the actual data format: check if data has entries array
      if (data.entries && Array.isArray(data.entries) && data.entries.length > 0) {
        console.log(`📊 Data found in entries array, processing ${data.entries.length} entries`);
        
        // Process ALL entries, not just the first one
        data.entries.forEach((entryData, index) => {
          console.log(`📥 Processing entry ${index + 1}:`, entryData);
          processEntryData(entryData);
        });
        
        // If no entries were processed, add one empty entry
        if (dreamEntries.length === 0) {
          console.log('No entries processed, adding empty entry');
          addDreamEntry();
        }
        
        console.log(`✅ Loaded ${dreamEntries.length} dream entries`);
        return;
      }
      
      // Fallback: process single data object
      console.log('Processing single data object:', data);
      processEntryData(data);
    };

    // Helper function to process individual entry data
    function processEntryData(actualData) {
      console.log('Processing entry data:', actualData);
      
      // Try to load from dreams_data (JSON format) first
      if (actualData.dreams_data) {
        try {
          const parsedDreams = JSON.parse(actualData.dreams_data);
          console.log('Loading from dreams_data JSON:', parsedDreams);
          
          parsedDreams.forEach(dreamData => {
            dreamEntryCounter++;
            const entry = {
              id: dreamData.id || dreamEntryCounter,
              record_id: dreamData.record_id || `${getCurrentAgentName() || 'Unknown'}_dream_${dreamData.id}_${Date.now()}`,
              timeFrame: normalizeTimeFrame(dreamData.timeFrame || dreamData.time_frame || ''),
              dream: dreamData.dream || '',
              why: dreamData.why || ''
            };
            dreamEntries.push(entry);
            dreamEntryCounter = Math.max(dreamEntryCounter, entry.id);
            console.log('📥 Loading dream from JSON:', entry);
            renderDreamEntry(entry);
          });
          
        } catch (error) {
          console.log('Failed to parse dreams_data JSON:', error);
        }
      }
      
      // Try to load from individual fields if there's data
      if (actualData.time_frame || actualData.dream || actualData.why) {
        console.log('Loading from individual fields:', {
          time_frame: actualData.time_frame,
          dream: actualData.dream,
          why: actualData.why
        });
        
        dreamEntryCounter++;
        const entry = {
          id: dreamEntryCounter,
          record_id: actualData.record_id || `${getCurrentAgentName() || 'Unknown'}_dream_${dreamEntryCounter}_${Date.now()}`,
          timeFrame: normalizeTimeFrame(actualData.time_frame ? actualData.time_frame.toString() : ''),
          dream: actualData.dream ? actualData.dream.toString() : '',
          why: actualData.why ? actualData.why.toString() : ''
        };
        
        console.log('📥 Loading dream from individual fields:', entry);
        
        // Only add if there's actual content
        if (entry.timeFrame || entry.dream || entry.why) {
          dreamEntries.push(entry);
          renderDreamEntry(entry);
          console.log('Created dream entry:', entry);
        }
      }
      
      // Fallback: try to extract from nested format (for backward compatibility)
      if (dreamEntries.length === 0) {
        console.log('Trying nested format extraction');
        const dreamsData = {};
        Object.keys(actualData).forEach(key => {
          if (key.startsWith('dreams[')) {
            // Parse dreams[1][time_frame] format
            const match = key.match(/dreams\[(\d+)\]\[(\w+)\]/);
            if (match) {
              const entryId = parseInt(match[1]);
              const field = match[2];
              if (!dreamsData[entryId]) {
                dreamsData[entryId] = { id: entryId };
              }
              dreamsData[entryId][field] = actualData[key];
            }
          }
        });
        
        // Convert to dream entries and render
        Object.values(dreamsData).forEach(dreamData => {
          if (dreamData.time_frame || dreamData.dream || dreamData.why) {
            const entry = {
              id: dreamData.id,
              record_id: dreamData.record_id || `${getCurrentAgentName() || 'Unknown'}_dream_${dreamData.id}_${Date.now()}`,
              timeFrame: normalizeTimeFrame(dreamData.time_frame || ''),
              dream: dreamData.dream || '',
              why: dreamData.why || ''
            };
            
            console.log('📥 Loading dream from nested format:', entry);
            dreamEntries.push(entry);
            dreamEntryCounter = Math.max(dreamEntryCounter, entry.id);
            renderDreamEntry(entry);
          }
        });
      }
      
      console.log('Dreams loaded successfully:', dreamEntries);
      
      // Update the form data to match loaded entries
      updateDreamsFormData();
      
      // Show success message if entries were loaded
      if (dreamEntries.length > 0) {
        showDreamsStatus(`Loaded ${dreamEntries.length} dream entries`, 'success');
        if (typeof window.toastManager !== 'undefined') {
          window.toastManager.show(`Loaded ${dreamEntries.length} dream entries successfully!`, 'success');
        }
      } else {
        // Add one empty entry for new users if no dreams are loaded
        if (dreamEntries.length === 0) {
          console.log('No dream entries found, adding empty entry');
          addDreamEntry();
        }
      }
    }

    // Override the external script's populateForm function to handle special forms
    const originalPopulateForm = window.populateForm;
    window.populateForm = function(formId, data) {
      if (formId === 'dreamsForm') {
        // Handle dreams form specially
        console.log('Dreams form population intercepted');
        
        // First, handle basic fields (record_id, agentName)
        const form = document.getElementById(formId);
        if (form) {
          const recordIdInput = form.querySelector('input[name="record_id"]');
          if (recordIdInput && data.record_id) {
            recordIdInput.value = data.record_id;
          }
          
          const agentNameInput = form.querySelector('input[name="agentName"]');
          if (agentNameInput && data.agentName) {
            agentNameInput.value = data.agentName;
          }
        }
        
        // Then handle dreams data
        window.populateDreamsForm(data);
        
      } else if (formId === 'expensesForm') {
        // Handle expenses form specially
        console.log('Expenses form population intercepted');
        
        const form = document.getElementById(formId);
        if (form) {
          const recordIdInput = form.querySelector('input[name="record_id"]');
          if (recordIdInput && data.record_id) {
            recordIdInput.value = data.record_id;
          }
          
          const agentNameInput = form.querySelector('input[name="agentName"]');
          if (agentNameInput && data.agentName) {
            agentNameInput.value = data.agentName;
          }
        }
        
        window.populateExpensesForm(data);
        
      } else if (formId === 'partnersForm') {
        // Handle partners form specially
        console.log('Partners form population intercepted');
        
        const form = document.getElementById(formId);
        if (form) {
          const recordIdInput = form.querySelector('input[name="record_id"]');
          if (recordIdInput && data.record_id) {
            recordIdInput.value = data.record_id;
          }
          
          const agentNameInput = form.querySelector('input[name="agentName"]');
          if (agentNameInput && data.agentName) {
            agentNameInput.value = data.agentName;
          }
        }
        
        window.populatePartnersForm(data);
        
      } else if (formId === 'clientsForm') {
        // Handle clients form specially
        console.log('Clients form population intercepted');
        
        const form = document.getElementById(formId);
        if (form) {
          const recordIdInput = form.querySelector('input[name="record_id"]');
          if (recordIdInput && data.record_id) {
            recordIdInput.value = data.record_id;
          }
          
          const agentNameInput = form.querySelector('input[name="agentName"]');
          if (agentNameInput && data.agentName) {
            agentNameInput.value = data.agentName;
          }
        }
        
        window.populateClientsForm(data);
        
      } else if (originalPopulateForm) {
        // Use original function for other forms
        originalPopulateForm(formId, data);
      }
    };

    // Load dreams when page loads
    setTimeout(waitForExternalScript, 1000);

    // ============================================================================
    // EXPENSES TO INCOME REPORT FUNCTIONALITY
    // ============================================================================
    
    let expenseEntries = [];
    let expenseEntryCounter = 0;

    window.addExpenseEntry = function addExpenseEntry() {
      expenseEntryCounter++;
      const entry = {
        id: expenseEntryCounter,
        item: '',
        amount: '',
        category: '',
        date: '',
        description: ''
      };
      expenseEntries.push(entry);
      renderExpenseEntry(entry);
      updateExpensesFormData();
    }

    function renderExpenseEntry(entry) {
      const tbody = document.querySelector('#expensesTable tbody');
      if (!tbody) return;
      
      const row = document.createElement('tr');
      row.setAttribute('data-entry-id', entry.id);
      
      row.innerHTML = `
        <td><input type="text" class="form-control expense-item" value="${entry.item}" placeholder="e.g., Office supplies"></td>
        <td><input type="number" class="form-control expense-amount" value="${entry.amount}" placeholder="0.00" step="0.01"></td>
        <td><select class="form-control expense-category">
          <option value="Business" ${entry.category === 'Business' ? 'selected' : ''}>Business</option>
          <option value="Marketing" ${entry.category === 'Marketing' ? 'selected' : ''}>Marketing</option>
          <option value="Training" ${entry.category === 'Training' ? 'selected' : ''}>Training</option>
          <option value="Travel" ${entry.category === 'Travel' ? 'selected' : ''}>Travel</option>
          <option value="Equipment" ${entry.category === 'Equipment' ? 'selected' : ''}>Equipment</option>
          <option value="Other" ${entry.category === 'Other' ? 'selected' : ''}>Other</option>
        </select></td>
        <td><input type="date" class="form-control expense-date" value="${entry.date}"></td>
        <td><input type="text" class="form-control expense-description" value="${entry.description}" placeholder="Brief description"></td>
        <td class="table-actions">
          <button type="button" onclick="removeExpenseEntry(${entry.id})" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      `;
      
      tbody.appendChild(row);
      
      // Add auto-save listeners
      row.querySelectorAll('input, select').forEach(input => {
        input.addEventListener('input', function() {
          const entryIndex = expenseEntries.findIndex(e => e.id === entry.id);
          if (entryIndex !== -1) {
            expenseEntries[entryIndex].item = row.querySelector('.expense-item').value;
            expenseEntries[entryIndex].amount = row.querySelector('.expense-amount').value;
            expenseEntries[entryIndex].category = row.querySelector('.expense-category').value;
            expenseEntries[entryIndex].date = row.querySelector('.expense-date').value;
            expenseEntries[entryIndex].description = row.querySelector('.expense-description').value;
            
            updateExpensesFormData();
            
            clearTimeout(window.expenseAutoSaveTimeout);
            window.expenseAutoSaveTimeout = setTimeout(() => {
              if (window.currentAgent && window.currentAgent.agentName) {
                window.saveExpenses();
              } else {
                console.log('Auto-save skipped: No user logged in');
              }
            }, 1000);
          }
        });
      });
    }

    window.removeExpenseEntry = function removeExpenseEntry(entryId) {
      expenseEntries = expenseEntries.filter(entry => entry.id !== entryId);
      const row = document.querySelector(`#expensesTable tr[data-entry-id="${entryId}"]`);
      if (row) {
        row.remove();
      }
      updateExpensesFormData();
    }

    function updateExpensesFormData() {
      document.querySelectorAll('#expensesForm input[name^="expenses"]').forEach(input => {
        if (input.type === 'hidden') {
          input.remove();
        }
      });
      
      const form = document.getElementById('expensesForm');
      if (!form) return;
      
      if (expenseEntries.length > 0) {
        const expensesJsonInput = document.createElement('input');
        expensesJsonInput.type = 'hidden';
        expensesJsonInput.name = 'expenses_data';
        expensesJsonInput.value = JSON.stringify(expenseEntries);
        form.appendChild(expensesJsonInput);
        
        const firstExpense = expenseEntries[0];
        if (firstExpense) {
          const itemInput = document.createElement('input');
          itemInput.type = 'hidden';
          itemInput.name = 'item';
          itemInput.value = firstExpense.item || '';
          form.appendChild(itemInput);
          
          const amountInput = document.createElement('input');
          amountInput.type = 'hidden';
          amountInput.name = 'amount';
          amountInput.value = firstExpense.amount || '';
          form.appendChild(amountInput);
          
          const categoryInput = document.createElement('input');
          categoryInput.type = 'hidden';
          categoryInput.name = 'category';
          categoryInput.value = firstExpense.category || '';
          form.appendChild(categoryInput);
          
          const dateInput = document.createElement('input');
          dateInput.type = 'hidden';
          dateInput.name = 'date';
          dateInput.value = firstExpense.date || '';
          form.appendChild(dateInput);
          
          const descriptionInput = document.createElement('input');
          descriptionInput.type = 'hidden';
          descriptionInput.name = 'description';
          descriptionInput.value = firstExpense.description || '';
          form.appendChild(descriptionInput);
        }
        
        const countInput = document.createElement('input');
        countInput.type = 'hidden';
        countInput.name = 'expenses_count';
        countInput.value = expenseEntries.length.toString();
        form.appendChild(countInput);
      }
    }

    window.saveExpenses = async function saveExpenses(event) {
      if (event) {
        event.preventDefault();
      }

      document.querySelectorAll('#expensesTable tbody tr').forEach((row, index) => {
        const entryIndex = expenseEntries.findIndex(entry => entry.id == row.getAttribute('data-entry-id'));
        if (entryIndex !== -1) {
          expenseEntries[entryIndex].item = row.querySelector('.expense-item').value;
          expenseEntries[entryIndex].amount = row.querySelector('.expense-amount').value;
          expenseEntries[entryIndex].category = row.querySelector('.expense-category').value;
          expenseEntries[entryIndex].date = row.querySelector('.expense-date').value;
          expenseEntries[entryIndex].description = row.querySelector('.expense-description').value;
        }
      });

      updateExpensesFormData();

      const statusElement = document.getElementById('expensesFormSaveStatus');
      if (statusElement) {
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
        statusElement.className = 'text-sm text-blue-500 mr-auto flex items-center';
      }

      if (typeof window.saveFormData === 'function') {
        if (!window.currentAgent || !window.currentAgent.agentName) {
          console.error('Cannot save expenses: No current agent logged in');
          if (statusElement) {
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Please log in first';
            statusElement.className = 'text-sm text-red-500 mr-auto flex items-center';
          }
          return;
        }
        
        const recordId = window.currentAgent.agentName + '_expensesForm';
        const recordIdElement = document.getElementById('expensesRecordId');
        const agentNameElement = document.getElementById('expensesAgentName');
        
        if (recordIdElement) recordIdElement.value = recordId;
        if (agentNameElement) agentNameElement.value = window.currentAgent.agentName;
        
        await window.saveFormData('expensesForm');
        
        if (statusElement) {
          statusElement.innerHTML = '<i class="fas fa-check mr-2"></i>Saved successfully!';
          statusElement.className = 'text-sm text-green-500 mr-auto flex items-center';
          setTimeout(() => {
            statusElement.innerHTML = '';
          }, 2000);
        }
      }
    }

    window.populateExpensesForm = function(data) {
      expenseEntries = [];
      expenseEntryCounter = 0;
      const tbody = document.querySelector('#expensesTable tbody');
      if (tbody) {
        tbody.innerHTML = '';
      }
      
      let actualData = data;
      if (data.entries && Array.isArray(data.entries) && data.entries.length > 0) {
        actualData = data.entries[0];
      }
      
      if (actualData.expenses_data) {
        try {
          const parsedExpenses = JSON.parse(actualData.expenses_data);
          parsedExpenses.forEach(expenseData => {
            const entry = {
              id: expenseData.id || (expenseEntryCounter + 1),
              item: expenseData.item || '',
              amount: expenseData.amount || '',
              category: expenseData.category || '',
              date: expenseData.date || '',
              description: expenseData.description || ''
            };
            expenseEntries.push(entry);
            expenseEntryCounter = Math.max(expenseEntryCounter, entry.id);
            renderExpenseEntry(entry);
          });
        } catch (error) {
          console.log('Failed to parse expenses_data JSON:', error);
        }
      }
      
      if (expenseEntries.length === 0 && (actualData.item || actualData.amount || actualData.category)) {
        const entry = {
          id: 1,
          item: actualData.item ? actualData.item.toString() : '',
          amount: actualData.amount ? actualData.amount.toString() : '',
          category: actualData.category ? actualData.category.toString() : '',
          date: actualData.date ? actualData.date.toString() : '',
          description: actualData.description ? actualData.description.toString() : ''
        };
        
        if (entry.item || entry.amount || entry.category) {
          expenseEntries.push(entry);
          expenseEntryCounter = 1;
          renderExpenseEntry(entry);
        }
      }
      
      updateExpensesFormData();
    };

    document.getElementById('addExpenseEntryBtn')?.addEventListener('click', window.addExpenseEntry);
    document.getElementById('expensesForm')?.addEventListener('submit', window.saveExpenses);

    // ============================================================================
    // POTENTIAL BUSINESS PARTNERS FUNCTIONALITY
    // ============================================================================
    
    let partnerEntries = [];
    let partnerEntryCounter = 0;

    window.addPartnerEntry = function addPartnerEntry() {
      partnerEntryCounter++;
      const entry = {
        id: partnerEntryCounter,
        name: '',
        contact: '',
        email: '',
        status: '',
        notes: ''
      };
      partnerEntries.push(entry);
      renderPartnerEntry(entry);
      updatePartnersFormData();
    }

    function renderPartnerEntry(entry) {
      const tbody = document.querySelector('#partnersTable tbody');
      if (!tbody) return;
      
      const row = document.createElement('tr');
      row.setAttribute('data-entry-id', entry.id);
      
      row.innerHTML = `
        <td><input type="text" class="form-control partner-name" value="${entry.name}" placeholder="Full Name"></td>
        <td><input type="text" class="form-control partner-contact" value="${entry.contact}" placeholder="Phone Number"></td>
        <td><input type="email" class="form-control partner-email" value="${entry.email}" placeholder="<EMAIL>"></td>
        <td><select class="form-control partner-status">
          <option value="Prospect" ${entry.status === 'Prospect' ? 'selected' : ''}>Prospect</option>
          <option value="Contacted" ${entry.status === 'Contacted' ? 'selected' : ''}>Contacted</option>
          <option value="Meeting Scheduled" ${entry.status === 'Meeting Scheduled' ? 'selected' : ''}>Meeting Scheduled</option>
          <option value="In Discussion" ${entry.status === 'In Discussion' ? 'selected' : ''}>In Discussion</option>
          <option value="Closed" ${entry.status === 'Closed' ? 'selected' : ''}>Closed</option>
          <option value="Not Interested" ${entry.status === 'Not Interested' ? 'selected' : ''}>Not Interested</option>
        </select></td>
        <td><input type="text" class="form-control partner-notes" value="${entry.notes}" placeholder="Additional notes"></td>
        <td class="table-actions">
          <button type="button" onclick="removePartnerEntry(${entry.id})" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      `;
      
      tbody.appendChild(row);
      
      // Add auto-save listeners
      row.querySelectorAll('input, select').forEach(input => {
        input.addEventListener('input', function() {
          const entryIndex = partnerEntries.findIndex(e => e.id === entry.id);
          if (entryIndex !== -1) {
            partnerEntries[entryIndex].name = row.querySelector('.partner-name').value;
            partnerEntries[entryIndex].contact = row.querySelector('.partner-contact').value;
            partnerEntries[entryIndex].email = row.querySelector('.partner-email').value;
            partnerEntries[entryIndex].status = row.querySelector('.partner-status').value;
            partnerEntries[entryIndex].notes = row.querySelector('.partner-notes').value;
            
            updatePartnersFormData();
            
            clearTimeout(window.partnerAutoSaveTimeout);
            window.partnerAutoSaveTimeout = setTimeout(() => {
              if (window.currentAgent && window.currentAgent.agentName) {
                window.savePartners();
              } else {
                console.log('Auto-save skipped: No user logged in');
              }
            }, 1000);
          }
        });
      });
    }

    window.removePartnerEntry = function removePartnerEntry(entryId) {
      partnerEntries = partnerEntries.filter(entry => entry.id !== entryId);
      const row = document.querySelector(`#partnersTable tr[data-entry-id="${entryId}"]`);
      if (row) {
        row.remove();
      }
      updatePartnersFormData();
    }

    function updatePartnersFormData() {
      document.querySelectorAll('#partnersForm input[name^="partners"]').forEach(input => {
        if (input.type === 'hidden') {
          input.remove();
        }
      });
      
      const form = document.getElementById('partnersForm');
      if (!form) return;
      
      if (partnerEntries.length > 0) {
        const partnersJsonInput = document.createElement('input');
        partnersJsonInput.type = 'hidden';
        partnersJsonInput.name = 'partners_data';
        partnersJsonInput.value = JSON.stringify(partnerEntries);
        form.appendChild(partnersJsonInput);
        
        const firstPartner = partnerEntries[0];
        if (firstPartner) {
          const nameInput = document.createElement('input');
          nameInput.type = 'hidden';
          nameInput.name = 'name';
          nameInput.value = firstPartner.name || '';
          form.appendChild(nameInput);
          
          const contactInput = document.createElement('input');
          contactInput.type = 'hidden';
          contactInput.name = 'contact';
          contactInput.value = firstPartner.contact || '';
          form.appendChild(contactInput);
          
          const emailInput = document.createElement('input');
          emailInput.type = 'hidden';
          emailInput.name = 'email';
          emailInput.value = firstPartner.email || '';
          form.appendChild(emailInput);
          
          const statusInput = document.createElement('input');
          statusInput.type = 'hidden';
          statusInput.name = 'status';
          statusInput.value = firstPartner.status || '';
          form.appendChild(statusInput);
          
          const notesInput = document.createElement('input');
          notesInput.type = 'hidden';
          notesInput.name = 'notes';
          notesInput.value = firstPartner.notes || '';
          form.appendChild(notesInput);
        }
        
        const countInput = document.createElement('input');
        countInput.type = 'hidden';
        countInput.name = 'partners_count';
        countInput.value = partnerEntries.length.toString();
        form.appendChild(countInput);
      }
    }

    window.savePartners = async function savePartners(event) {
      if (event) {
        event.preventDefault();
      }

      document.querySelectorAll('#partnersTable tbody tr').forEach((row, index) => {
        const entryIndex = partnerEntries.findIndex(entry => entry.id == row.getAttribute('data-entry-id'));
        if (entryIndex !== -1) {
          partnerEntries[entryIndex].name = row.querySelector('.partner-name').value;
          partnerEntries[entryIndex].contact = row.querySelector('.partner-contact').value;
          partnerEntries[entryIndex].email = row.querySelector('.partner-email').value;
          partnerEntries[entryIndex].status = row.querySelector('.partner-status').value;
          partnerEntries[entryIndex].notes = row.querySelector('.partner-notes').value;
        }
      });

      updatePartnersFormData();

      const statusElement = document.getElementById('partnersFormSaveStatus');
      if (statusElement) {
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
        statusElement.className = 'text-sm text-blue-500 mr-auto flex items-center';
      }

      if (typeof window.saveFormData === 'function') {
        if (!window.currentAgent || !window.currentAgent.agentName) {
          console.error('Cannot save partners: No current agent logged in');
          if (statusElement) {
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Please log in first';
            statusElement.className = 'text-sm text-red-500 mr-auto flex items-center';
          }
          return;
        }
        
        const recordId = window.currentAgent.agentName + '_partnersForm';
        const recordIdElement = document.getElementById('partnersRecordId');
        const agentNameElement = document.getElementById('partnersAgentName');
        
        if (recordIdElement) recordIdElement.value = recordId;
        if (agentNameElement) agentNameElement.value = window.currentAgent.agentName;
        
        await window.saveFormData('partnersForm');
        
        if (statusElement) {
          statusElement.innerHTML = '<i class="fas fa-check mr-2"></i>Saved successfully!';
          statusElement.className = 'text-sm text-green-500 mr-auto flex items-center';
          setTimeout(() => {
            statusElement.innerHTML = '';
          }, 2000);
        }
      }
    }

    window.populatePartnersForm = function(data) {
      partnerEntries = [];
      partnerEntryCounter = 0;
      const tbody = document.querySelector('#partnersTable tbody');
      if (tbody) {
        tbody.innerHTML = '';
      }
      
      let actualData = data;
      if (data.entries && Array.isArray(data.entries) && data.entries.length > 0) {
        actualData = data.entries[0];
      }
      
      if (actualData.partners_data) {
        try {
          const parsedPartners = JSON.parse(actualData.partners_data);
          parsedPartners.forEach(partnerData => {
            const entry = {
              id: partnerData.id || (partnerEntryCounter + 1),
              name: partnerData.name || '',
              contact: partnerData.contact || '',
              email: partnerData.email || '',
              status: partnerData.status || '',
              notes: partnerData.notes || ''
            };
            partnerEntries.push(entry);
            partnerEntryCounter = Math.max(partnerEntryCounter, entry.id);
            renderPartnerEntry(entry);
          });
        } catch (error) {
          console.log('Failed to parse partners_data JSON:', error);
        }
      }
      
      if (partnerEntries.length === 0 && (actualData.name || actualData.contact || actualData.email)) {
        const entry = {
          id: 1,
          name: actualData.name ? actualData.name.toString() : '',
          contact: actualData.contact ? actualData.contact.toString() : '',
          email: actualData.email ? actualData.email.toString() : '',
          status: actualData.status ? actualData.status.toString() : '',
          notes: actualData.notes ? actualData.notes.toString() : ''
        };
        
        if (entry.name || entry.contact || entry.email) {
          partnerEntries.push(entry);
          partnerEntryCounter = 1;
          renderPartnerEntry(entry);
        }
      }
      
      updatePartnersFormData();
    };

    document.getElementById('addPartnerEntryBtn')?.addEventListener('click', window.addPartnerEntry);
    document.getElementById('partnersForm')?.addEventListener('submit', window.savePartners);

    // ============================================================================
    // POTENTIAL FIELD TRAININGS FUNCTIONALITY
    // ============================================================================
    
    let clientEntries = [];
    let clientEntryCounter = 0;

    window.addClientEntry = function addClientEntry() {
      clientEntryCounter++;
      const entry = {
        id: clientEntryCounter,
        clientName: '',
        date: '',
        trainingType: '',
        outcome: '',
        notes: ''
      };
      clientEntries.push(entry);
      renderClientEntry(entry);
      updateClientsFormData();
    }

    function renderClientEntry(entry) {
      const tbody = document.querySelector('#trainingsTable tbody');
      if (!tbody) return;
      
      const row = document.createElement('tr');
      row.setAttribute('data-entry-id', entry.id);
      
      row.innerHTML = `
        <td><input type="text" class="form-control client-name" value="${entry.clientName}" placeholder="Client Name"></td>
        <td><input type="date" class="form-control client-date" value="${entry.date}"></td>
        <td><select class="form-control client-training-type">
          <option value="Product Training" ${entry.trainingType === 'Product Training' ? 'selected' : ''}>Product Training</option>
          <option value="Sales Training" ${entry.trainingType === 'Sales Training' ? 'selected' : ''}>Sales Training</option>
          <option value="Compliance Training" ${entry.trainingType === 'Compliance Training' ? 'selected' : ''}>Compliance Training</option>
          <option value="Leadership Training" ${entry.trainingType === 'Leadership Training' ? 'selected' : ''}>Leadership Training</option>
          <option value="System Training" ${entry.trainingType === 'System Training' ? 'selected' : ''}>System Training</option>
          <option value="Other" ${entry.trainingType === 'Other' ? 'selected' : ''}>Other</option>
        </select></td>
        <td><select class="form-control client-outcome">
          <option value="Completed" ${entry.outcome === 'Completed' ? 'selected' : ''}>Completed</option>
          <option value="In Progress" ${entry.outcome === 'In Progress' ? 'selected' : ''}>In Progress</option>
          <option value="Scheduled" ${entry.outcome === 'Scheduled' ? 'selected' : ''}>Scheduled</option>
          <option value="Cancelled" ${entry.outcome === 'Cancelled' ? 'selected' : ''}>Cancelled</option>
          <option value="Rescheduled" ${entry.outcome === 'Rescheduled' ? 'selected' : ''}>Rescheduled</option>
        </select></td>
        <td><input type="text" class="form-control client-notes" value="${entry.notes}" placeholder="Training notes"></td>
        <td class="table-actions">
          <button type="button" onclick="removeClientEntry(${entry.id})" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      `;
      
      tbody.appendChild(row);
      
      // Add auto-save listeners
      row.querySelectorAll('input, select').forEach(input => {
        input.addEventListener('input', function() {
          const entryIndex = clientEntries.findIndex(e => e.id === entry.id);
          if (entryIndex !== -1) {
            clientEntries[entryIndex].clientName = row.querySelector('.client-name').value;
            clientEntries[entryIndex].date = row.querySelector('.client-date').value;
            clientEntries[entryIndex].trainingType = row.querySelector('.client-training-type').value;
            clientEntries[entryIndex].outcome = row.querySelector('.client-outcome').value;
            clientEntries[entryIndex].notes = row.querySelector('.client-notes').value;
            
            updateClientsFormData();
            
            clearTimeout(window.clientAutoSaveTimeout);
            window.clientAutoSaveTimeout = setTimeout(() => {
              if (window.currentAgent && window.currentAgent.agentName) {
                window.saveClients();
              } else {
                console.log('Auto-save skipped: No user logged in');
              }
            }, 1000);
          }
        });
      });
    }

    window.removeClientEntry = function removeClientEntry(entryId) {
      clientEntries = clientEntries.filter(entry => entry.id !== entryId);
      const row = document.querySelector(`#trainingsTable tr[data-entry-id="${entryId}"]`);
      if (row) {
        row.remove();
      }
      updateClientsFormData();
    }

    function updateClientsFormData() {
      document.querySelectorAll('#clientsForm input[name^="clients"]').forEach(input => {
        if (input.type === 'hidden') {
          input.remove();
        }
      });
      
      const form = document.getElementById('clientsForm');
      if (!form) return;
      
      if (clientEntries.length > 0) {
        const clientsJsonInput = document.createElement('input');
        clientsJsonInput.type = 'hidden';
        clientsJsonInput.name = 'clients_data';
        clientsJsonInput.value = JSON.stringify(clientEntries);
        form.appendChild(clientsJsonInput);
        
        const firstClient = clientEntries[0];
        if (firstClient) {
          const clientNameInput = document.createElement('input');
          clientNameInput.type = 'hidden';
          clientNameInput.name = 'client_name';
          clientNameInput.value = firstClient.clientName || '';
          form.appendChild(clientNameInput);
          
          const dateInput = document.createElement('input');
          dateInput.type = 'hidden';
          dateInput.name = 'date';
          dateInput.value = firstClient.date || '';
          form.appendChild(dateInput);
          
          const trainingTypeInput = document.createElement('input');
          trainingTypeInput.type = 'hidden';
          trainingTypeInput.name = 'training_type';
          trainingTypeInput.value = firstClient.trainingType || '';
          form.appendChild(trainingTypeInput);
          
          const outcomeInput = document.createElement('input');
          outcomeInput.type = 'hidden';
          outcomeInput.name = 'outcome';
          outcomeInput.value = firstClient.outcome || '';
          form.appendChild(outcomeInput);
          
          const notesInput = document.createElement('input');
          notesInput.type = 'hidden';
          notesInput.name = 'notes';
          notesInput.value = firstClient.notes || '';
          form.appendChild(notesInput);
        }
        
        const countInput = document.createElement('input');
        countInput.type = 'hidden';
        countInput.name = 'clients_count';
        countInput.value = clientEntries.length.toString();
        form.appendChild(countInput);
      }
    }

    window.saveClients = async function saveClients(event) {
      if (event) {
        event.preventDefault();
      }

      document.querySelectorAll('#trainingsTable tbody tr').forEach((row, index) => {
        const entryIndex = clientEntries.findIndex(entry => entry.id == row.getAttribute('data-entry-id'));
        if (entryIndex !== -1) {
          clientEntries[entryIndex].clientName = row.querySelector('.client-name').value;
          clientEntries[entryIndex].date = row.querySelector('.client-date').value;
          clientEntries[entryIndex].trainingType = row.querySelector('.client-training-type').value;  
          clientEntries[entryIndex].outcome = row.querySelector('.client-outcome').value;
          clientEntries[entryIndex].notes = row.querySelector('.client-notes').value;
        }
      });

      updateClientsFormData();

      const statusElement = document.getElementById('clientsFormSaveStatus');
      if (statusElement) {
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
        statusElement.className = 'text-sm text-blue-500 mr-auto flex items-center';
      }

      if (typeof window.saveFormData === 'function') {
        if (!window.currentAgent || !window.currentAgent.agentName) {
          console.error('Cannot save clients: No current agent logged in');
          if (statusElement) {
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Please log in first';
            statusElement.className = 'text-sm text-red-500 mr-auto flex items-center';
          }
          return;
        }
        
        const recordId = window.currentAgent.agentName + '_clientsForm';
        const recordIdElement = document.getElementById('clientsRecordId');
        const agentNameElement = document.getElementById('clientsAgentName');
        
        if (recordIdElement) recordIdElement.value = recordId;
        if (agentNameElement) agentNameElement.value = window.currentAgent.agentName;
        
        await window.saveFormData('clientsForm');
        
        if (statusElement) {
          statusElement.innerHTML = '<i class="fas fa-check mr-2"></i>Saved successfully!';
          statusElement.className = 'text-sm text-green-500 mr-auto flex items-center';
          setTimeout(() => {
            statusElement.innerHTML = '';
          }, 2000);
        }
      }
    }

    window.populateClientsForm = function(data) {
      clientEntries = [];
      clientEntryCounter = 0;
      const tbody = document.querySelector('#trainingsTable tbody');
      if (tbody) {
        tbody.innerHTML = '';
      }
      
      let actualData = data;
      if (data.entries && Array.isArray(data.entries) && data.entries.length > 0) {
        actualData = data.entries[0];
      }
      
      if (actualData.clients_data) {
        try {
          const parsedClients = JSON.parse(actualData.clients_data);
          parsedClients.forEach(clientData => {
            const entry = {
              id: clientData.id || (clientEntryCounter + 1),
              clientName: clientData.clientName || '',
              date: clientData.date || '',
              trainingType: clientData.trainingType || '',
              outcome: clientData.outcome || '',
              notes: clientData.notes || ''
            };
            clientEntries.push(entry);
            clientEntryCounter = Math.max(clientEntryCounter, entry.id);
            renderClientEntry(entry);
          });
        } catch (error) {
          console.log('Failed to parse clients_data JSON:', error);
        }
      }
      
      if (clientEntries.length === 0 && (actualData.client_name || actualData.date || actualData.training_type)) {
        const entry = {
          id: 1,
          clientName: actualData.client_name ? actualData.client_name.toString() : '',
          date: actualData.date ? actualData.date.toString() : '',
          trainingType: actualData.training_type ? actualData.training_type.toString() : '',
          outcome: actualData.outcome ? actualData.outcome.toString() : '',
          notes: actualData.notes ? actualData.notes.toString() : ''
        };
        
        if (entry.clientName || entry.date || entry.trainingType) {
          clientEntries.push(entry);
          clientEntryCounter = 1;
          renderClientEntry(entry);
        }
      }
      
      updateClientsFormData();
    };

    document.getElementById('addTrainingEntryBtn')?.addEventListener('click', window.addClientEntry);
    document.getElementById('clientsForm')?.addEventListener('submit', window.saveClients);

    // Auto-calculate net income for expenses form
    const totalIncomeInput = document.getElementById('totalIncome');
    const totalExpensesInput = document.getElementById('totalExpenses');
    const netIncomeInput = document.getElementById('netIncome');
    
    function calculateNetIncome() {
      const income = parseFloat(totalIncomeInput.value) || 0;
      const expenses = parseFloat(totalExpensesInput.value) || 0;
      const net = income - expenses;
      if (netIncomeInput) {
        netIncomeInput.value = net.toFixed(2);
      }
    }
    
    if (totalIncomeInput && totalExpensesInput) {
      totalIncomeInput.addEventListener('input', calculateNetIncome);
      totalExpensesInput.addEventListener('input', calculateNetIncome);
    }
    });
    
    // Enhanced auto-save toggle
    function toggleAutoSave() {
      if (typeof autoSaveEnabled !== 'undefined') {
        autoSaveEnabled = !autoSaveEnabled;
        const toggleButton = document.querySelector('.auto-save-toggle');
        const toggleText = toggleButton ? toggleButton.querySelector('.auto-save-text') : null;
        
        if (autoSaveEnabled) {
          if (toggleText) toggleText.textContent = 'Disable Auto-Save';
          if (toggleButton) {
            toggleButton.classList.remove('btn-success');
            toggleButton.classList.add('btn-secondary');
          }
          if (typeof toastManager !== 'undefined') {
            toastManager.show('Auto-save enabled', 'success');
          }
        } else {
          if (toggleText) toggleText.textContent = 'Enable Auto-Save';
          if (toggleButton) {
            toggleButton.classList.remove('btn-secondary');
            toggleButton.classList.add('btn-success');
          }
          if (typeof toastManager !== 'undefined') {
            toastManager.show('Auto-save disabled', 'info');
          }
        }
      }
    }
  </script>
</body>
</html>
